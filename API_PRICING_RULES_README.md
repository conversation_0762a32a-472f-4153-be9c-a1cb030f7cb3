# 价格规则管理功能

## 功能概述

价格规则管理功能允许管理员配置不同服务类型、模型的计费价格规则。支持优先级设置，灵活的模型匹配机制。

## 后端实现

### 1. 数据模型 (`model/billing_key.go`)

```go
type BillingPricingRule struct {
    Model
    Module      string  `json:"module"`      // 服务类型: 'llm' | 'tts' | 'asr'
    ModelName   string  `json:"model_name"`  // 模型名称，可为空表示通用规则
    UnitPrice   float64 `json:"unit_price"`  // 单价
    Currency    string  `json:"currency"`    // 货币类型，默认 'CNY'
    Unit        string  `json:"unit"`        // 计费单位，默认 'token'
    IsActive    bool    `json:"is_active"`   // 是否激活，默认 true
    Priority    int     `json:"priority"`    // 优先级，数字越大优先级越高
    Description string  `json:"description"` // 规则描述
}
```

### 2. API 端点 (`api/billing/pricing_rule.go`)

使用 cosy 框架自动生成的 CRUD API：

- `GET /admin/billing/pricing_rules` - 获取规则列表
- `POST /admin/billing/pricing_rules` - 创建新规则
- `GET /admin/billing/pricing_rules/:id` - 获取单个规则
- `POST /admin/billing/pricing_rules/:id` - 更新规则
- `DELETE /admin/billing/pricing_rules/:id` - 删除规则

### 3. 价格匹配机制

计费系统会根据以下优先级匹配价格规则：

1. **精确匹配**：`module` + `model_name` 完全匹配
2. **通用匹配**：`module` 匹配且 `model_name` 为空
3. **优先级排序**：如果有多个匹配规则，按 `priority` 降序，`model_name` 降序

### 4. 缓存机制

- 价格规则会被缓存到 Redis 中
- 缓存 key 格式：`pricing_rule:{module}:{model_name}`
- 服务启动时会预加载所有激活的规则
- 规则更新时会自动刷新缓存

## 前端实现

### 1. API 接口 (`apps/admin/src/api/pricing_rule.ts`)

```typescript
export interface PricingRule extends ModelBase {
  module: 'llm' | 'tts' | 'asr'
  model_name: string
  unit_price: number
  currency: string
  unit: string
  is_active: boolean
  priority: number
  description: string
}

export const pricingRuleApi = useCurdApi<PricingRule>('/admin/billing/pricing_rules')
```

### 2. 管理页面 (`apps/admin/src/views/billing/pricing/index.vue`)

功能特性：
- ✅ 规则列表展示（表格形式）
- ✅ 创建新规则（对话框表单）
- ✅ 编辑规则（对话框表单）
- ✅ 删除规则（确认对话框）
- ✅ 一键启用/禁用规则
- ✅ 实时数据加载和状态管理
- ✅ 错误处理和用户反馈

### 3. 表单字段

- **服务类型**：下拉选择（LLM服务、TTS服务、ASR服务）
- **模型名称**：文本输入（留空表示通用规则）
- **计费单位**：下拉选择（Token、字符、秒、请求）
- **单价**：数字输入（支持4位小数）
- **货币**：下拉选择（人民币、美元）
- **优先级**：数字输入
- **启用状态**：开关组件
- **描述**：文本域（可选）

## 使用流程

### 1. 启动后端服务

```bash
cd billing-sys-api
go run main.go
```

### 2. 启动前端管理界面

```bash
cd billing-web/apps/admin
pnpm dev
```

### 3. 访问价格规则管理

在浏览器中访问：`http://localhost:3000/billing/pricing`

### 4. 创建价格规则示例

**LLM 服务规则：**
- 服务类型：LLM服务
- 模型名称：gpt-3.5-turbo
- 计费单位：Token
- 单价：0.002
- 货币：人民币
- 优先级：1

**通用规则：**
- 服务类型：TTS服务
- 模型名称：（留空）
- 计费单位：字符
- 单价：0.00004
- 货币：人民币
- 优先级：0

## API 测试

使用提供的 HTTP 测试文件：`test/pricing_rule_test.http`

需要替换以下变量：
- `{{rule_id}}`：实际的规则 ID
- `your-admin-token-here`：有效的管理员令牌

## 注意事项

1. **权限要求**：需要管理员权限才能访问价格规则管理
2. **数据验证**：前后端都会验证必填字段和数据格式
3. **缓存一致性**：规则更新时会自动刷新相关缓存
4. **软删除**：删除操作是软删除，可通过数据库恢复
5. **优先级策略**：建议为精确匹配规则设置较高优先级

## 下一步功能扩展

- [ ] 批量导入/导出规则
- [ ] 规则生效时间调度
- [ ] 价格历史记录
- [ ] 规则使用统计
- [ ] 价格变更审批流程
