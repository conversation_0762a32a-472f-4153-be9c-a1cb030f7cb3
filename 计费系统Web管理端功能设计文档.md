# 计费系统Web管理端功能设计文档

## 1. 项目概述

### 1.1 系统简介
基于现有计费系统API构建的Web管理端，提供全面的API Key管理、用户管理、计费管理和系统配置功能。

### 1.2 技术栈
- **前端框架**: Vue 3 + TypeScript + Vite
- **UI组件库**: shadcn-vue
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **工具库**: VueUse
- **构建工具**: Vite
- **包管理器**: pnpm (monorepo)

### 1.3 架构设计
```
billing-web/
├── apps/
│   ├── admin/          # 管理端应用
│   └── client/         # 客户端应用（预留）
├── packages/
│   ├── ui/             # UI组件库
│   └── curd/           # CRUD操作组件库
```

## 2. 功能模块设计

### 2.1 认证与授权模块

#### 2.1.1 登录系统
**页面**: `/login`

**功能特性**:
- 邮箱密码登录
- 记住登录状态
- 登录失败次数限制
- IP禁用管理

**API接口**:
- `POST /login` - 邮箱密码登录
- `DELETE /logout` - 退出登录

#### 2.1.2 权限管理
- JWT Token 管理
- 路由守卫
- 用户状态检查（正常/禁用）

### 2.2 用户管理模块

#### 2.2.1 用户列表
**页面**: `/users`

**功能特性**:
- 用户列表展示
- 搜索筛选（姓名、邮箱、手机）
- 状态筛选（激活/禁用）
- 分页加载
- 批量操作

#### 2.2.2 用户详情/编辑
**页面**: `/users/:id`

**功能特性**:
- 基本信息编辑
- 头像上传
- 密码重置
- 状态切换
- 关联API Key展示

### 2.3 API Key管理模块

#### 2.3.1 Key列表管理
**页面**: `/billing/keys`

**功能特性**:
- Key列表展示
- 创建新Key
- Key状态管理（正常/阻止）
- 关联用户显示
- 快速状态查看

#### 2.3.2 Key详情页面
**页面**: `/billing/keys/:key`

**功能特性**:
- Key基本信息
- 配额管理（LLM/TTS/ASR）
- 用量统计图表
- 使用历史记录

#### 2.3.3 用量统计页面
**页面**: `/billing/keys/:key/stats`

**功能特性**:
- 时间范围选择（日/周/月）
- 分模块统计图表
- Token使用量趋势
- 成本分析
- 导出报表

### 2.4 计费规则管理

#### 2.4.1 价格规则配置
**页面**: `/billing/pricing`

**功能特性**:
- 价格规则列表
- 分模块价格设置
- 优先级管理
- 规则启用/禁用

### 2.5 系统设置模块

#### 2.5.1 认证设置
**页面**: `/settings/auth`

**功能特性**:
- 登录失败次数限制
- IP禁用时长设置
- 被禁IP管理
- 解除IP禁用

#### 2.5.2 通用设置
**页面**: `/settings/general`

**功能特性**:
- 系统配置管理
- 动态设置项
- 配置分类展示

### 2.6 文件管理模块

#### 2.6.1 文件上传管理
**页面**: `/uploads`

**功能特性**:
- 文件列表展示
- 分类筛选
- 文件预览
- 批量删除

## 3. 开发计划

### 3.1 第一阶段 (基础功能)
- [x] 项目架构搭建
- [ ] 用户认证系统
- [ ] 基础布局组件
- [ ] 用户管理模块
- [ ] API Key基础管理

### 3.2 第二阶段 (核心功能)
- [ ] 计费配额管理
- [ ] 用量统计展示
- [ ] 系统设置模块
- [ ] 文件上传管理

### 3.3 第三阶段 (高级功能)
- [ ] 数据可视化图表
- [ ] 报表导出功能
- [ ] 实时数据更新
- [ ] 高级搜索筛选
