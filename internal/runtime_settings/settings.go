package runtime_settings

import (
	"encoding/json"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/redis"
	"strings"
)

func buildKey(key interface{}) string {
	var sb strings.Builder
	sb.WriteString("settings:")
	sb.WriteString(cast.ToString(key))
	return sb.String()
}

func Get(key string, value interface{}) (err error) {
	key = buildKey(key)
	raw, err := redis.Get(key)
	if err != nil {
		return
	}
	return json.Unmarshal([]byte(raw), value)
}

func Set(key string, value interface{}) {
	key = buildKey(key)
	_ = redis.Set(key, value, 0)
}
