package billing

import (
	"context"
	"fmt"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// StatService 统计服务
type StatService struct {
	cacheService *CacheService
	config       *settings.BillingConfig
}

// NewStatService 创建统计服务
func NewStatService(cacheService *CacheService, config *settings.BillingConfig) *StatService {
	return &StatService{
		cacheService: cacheService,
		config:       config,
	}
}

// DashboardStats Dashboard统计数据结构
type DashboardStats struct {
	// 核心指标
	TotalRevenue float64 `json:"total_revenue"` // 总收入
	MonthRevenue float64 `json:"month_revenue"` // 本月收入
	TodayRevenue float64 `json:"today_revenue"` // 今日收入
	TotalUsers   int64   `json:"total_users"`   // 总用户数
	ActiveUsers  int64   `json:"active_users"`  // 活跃用户数
	TotalKeys    int64   `json:"total_keys"`    // 总Key数
	ActiveKeys   int64   `json:"active_keys"`   // 活跃Key数
	BlockedKeys  int64   `json:"blocked_keys"`  // 阻止Key数

	// 计费相关
	TotalQuotaPackages  int64   `json:"total_quota_packages"`  // 总资源包数
	ActiveQuotaPackages int64   `json:"active_quota_packages"` // 活跃资源包数
	TotalBalance        float64 `json:"total_balance"`         // 用户总余额
	AvgUserBalance      float64 `json:"avg_user_balance"`      // 平均用户余额

	// 使用量统计
	TotalUsage    int64 `json:"total_usage"`     // 总使用量
	MonthUsage    int64 `json:"month_usage"`     // 本月使用量
	TodayUsage    int64 `json:"today_usage"`     // 今日使用量
	AvgDailyUsage int64 `json:"avg_daily_usage"` // 平均日使用量

	// 趋势数据
	RevenueGrowth float64 `json:"revenue_growth"` // 收入增长率
	UserGrowth    float64 `json:"user_growth"`    // 用户增长率
	UsageGrowth   float64 `json:"usage_growth"`   // 使用量增长率
	BalanceGrowth float64 `json:"balance_growth"` // 余额增长率

	// 模块统计
	ModuleStats []ModuleStatsItem `json:"module_stats"`
}

// ModuleStatsItem 模块统计项
type ModuleStatsItem struct {
	Module  string  `json:"module"`  // 模块名称
	Usage   int64   `json:"usage"`   // 使用量
	Revenue float64 `json:"revenue"` // 收入
	Keys    int64   `json:"keys"`    // Key数量
	Growth  float64 `json:"growth"`  // 增长率
	Unit    string  `json:"unit"`    // 单位
}

// RechargeStats 充值统计数据结构
type RechargeStats struct {
	TodayAmount     float64 `json:"today_amount"`     // 今日充值金额
	MonthAmount     float64 `json:"month_amount"`     // 本月充值金额
	TotalCount      int64   `json:"total_count"`      // 总充值次数
	AverageAmount   float64 `json:"average_amount"`   // 平均充值金额
	PendingAmount   float64 `json:"pending_amount"`   // 待处理金额
	CompletedAmount float64 `json:"completed_amount"` // 已完成金额
	FailedAmount    float64 `json:"failed_amount"`    // 失败金额
	TotalUsers      int64   `json:"total_users"`      // 总用户数
	ActiveUsers     int64   `json:"active_users"`     // 活跃用户数
	TotalBalance    float64 `json:"total_balance"`    // 用户总余额
}

// KeyOverviewStats API Key概览统计
type KeyOverviewStats struct {
	TotalKeys       int64        `json:"total_keys"`        // 总Key数量
	ActiveKeys      int64        `json:"active_keys"`       // 活跃Key数量
	BlockedKeys     int64        `json:"blocked_keys"`      // 阻止Key数量
	KeysWithQuota   int64        `json:"keys_with_quota"`   // 有配额的Key数量
	KeysWithBalance int64        `json:"keys_with_balance"` // 用户有余额的Key数量
	TotalQuotaUsage float64      `json:"total_quota_usage"` // 总配额使用率
	AvgQuotaUsage   float64      `json:"avg_quota_usage"`   // 平均配额使用率
	ModuleStats     []ModuleStat `json:"module_stats"`      // 按模块统计
}

// ModuleStat 模块统计结构
type ModuleStat struct {
	Module      string  `json:"module"`       // 模块名称
	Name        string  `json:"name"`         // 模块显示名称
	KeyCount    int64   `json:"key_count"`    // Key数量
	ActiveCount int64   `json:"active_count"` // 活跃Key数量
	AvgUsage    float64 `json:"avg_usage"`    // 平均使用率
}

// UsageStats 使用量统计
type UsageStats struct {
	Period     string                 `json:"period"`      // 统计周期
	StartTime  int64                  `json:"start_time"`  // 开始时间
	EndTime    int64                  `json:"end_time"`    // 结束时间
	TotalCost  float64                `json:"total_cost"`  // 总成本
	TotalCount int64                  `json:"total_count"` // 总请求数
	ByModule   []ModuleUsageStatsItem `json:"by_module"`   // 按模块的详细统计
}

// ModuleUsageStatsItem 模块使用量统计项
type ModuleUsageStatsItem struct {
	Module     string  `json:"module"`      // 模块名称
	TotalUsage int64   `json:"total_usage"` // 总使用量
	TotalCost  float64 `json:"total_cost"`  // 总成本
	Count      int64   `json:"count"`       // 请求次数
	Unit       string  `json:"unit"`        // 单位
}

// GetDashboardStats 获取Dashboard统计数据
func (ss *StatService) GetDashboardStats(ctx context.Context) (*DashboardStats, error) {
	db := cosy.UseDB(ctx)

	// 获取当前时间
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	lastMonthStart := monthStart.AddDate(0, -1, 0)

	// 转换为毫秒时间戳
	todayStartMs := todayStart.UnixMilli()
	monthStartMs := monthStart.UnixMilli()
	lastMonthStartMs := lastMonthStart.UnixMilli()

	stats := &DashboardStats{}

	// 1. 用户统计
	if err := ss.getUserStats(db, stats); err != nil {
		logger.Error("获取用户统计失败", "error", err)
		return nil, err
	}

	// 2. API Key统计
	if err := ss.getKeyStats(db, stats); err != nil {
		logger.Error("获取Key统计失败", "error", err)
		return nil, err
	}

	// 3. 资源包统计
	if err := ss.getQuotaPackageStats(db, stats); err != nil {
		logger.Error("获取资源包统计失败", "error", err)
		return nil, err
	}

	// 4. 余额统计
	if err := ss.getBalanceStats(db, stats); err != nil {
		logger.Error("获取余额统计失败", "error", err)
		return nil, err
	}

	// 5. 收入统计
	if err := ss.getRevenueStats(db, stats, todayStartMs, monthStartMs); err != nil {
		logger.Error("获取收入统计失败", "error", err)
		return nil, err
	}

	// 6. 使用量统计
	if err := ss.getUsageStats(db, stats, todayStartMs, monthStartMs, now); err != nil {
		logger.Error("获取使用量统计失败", "error", err)
		return nil, err
	}

	// 7. 计算增长率
	if err := ss.calculateGrowthRates(db, stats, monthStartMs, lastMonthStartMs); err != nil {
		logger.Error("计算增长率失败", "error", err)
		return nil, err
	}

	// 8. 模块统计
	moduleStats, err := ss.getModuleStats(db, monthStartMs)
	if err != nil {
		logger.Error("获取模块统计失败", "error", err)
		return nil, err
	}
	stats.ModuleStats = moduleStats

	return stats, nil
}

// getUserStats 获取用户统计
func (ss *StatService) getUserStats(db *gorm.DB, stats *DashboardStats) error {
	// 总用户数
	if err := db.Model(&model.User{}).Count(&stats.TotalUsers).Error; err != nil {
		return fmt.Errorf("获取总用户数失败: %w", err)
	}

	// 活跃用户数（有余额的用户）
	if err := db.Model(&model.User{}).Where("balance > 0").Count(&stats.ActiveUsers).Error; err != nil {
		return fmt.Errorf("获取活跃用户数失败: %w", err)
	}

	return nil
}

// getKeyStats 获取API Key统计
func (ss *StatService) getKeyStats(db *gorm.DB, stats *DashboardStats) error {
	// 总Key数
	if err := db.Model(&model.ApiKey{}).Count(&stats.TotalKeys).Error; err != nil {
		return fmt.Errorf("获取总Key数失败: %w", err)
	}

	// 活跃Key数
	if err := db.Model(&model.ApiKey{}).Where("status = ?", "ok").Count(&stats.ActiveKeys).Error; err != nil {
		return fmt.Errorf("获取活跃Key数失败: %w", err)
	}

	// 阻止Key数
	if err := db.Model(&model.ApiKey{}).Where("status = ?", "blocked").Count(&stats.BlockedKeys).Error; err != nil {
		return fmt.Errorf("获取阻止Key数失败: %w", err)
	}

	return nil
}

// getQuotaPackageStats 获取资源包统计
func (ss *StatService) getQuotaPackageStats(db *gorm.DB, stats *DashboardStats) error {
	// 总资源包数
	if err := db.Model(&model.QuotaPackageRecord{}).Count(&stats.TotalQuotaPackages).Error; err != nil {
		return fmt.Errorf("获取总资源包数失败: %w", err)
	}

	// 活跃资源包数
	if err := db.Model(&model.QuotaPackageRecord{}).Where("status = ?", "active").Count(&stats.ActiveQuotaPackages).Error; err != nil {
		return fmt.Errorf("获取活跃资源包数失败: %w", err)
	}

	return nil
}

// getBalanceStats 获取余额统计
func (ss *StatService) getBalanceStats(db *gorm.DB, stats *DashboardStats) error {
	// 用户总余额
	if err := db.Model(&model.User{}).Select("COALESCE(SUM(balance), 0)").Scan(&stats.TotalBalance).Error; err != nil {
		return fmt.Errorf("获取用户总余额失败: %w", err)
	}

	// 平均用户余额
	if stats.TotalUsers > 0 {
		stats.AvgUserBalance = stats.TotalBalance / float64(stats.TotalUsers)
	}

	return nil
}

// getRevenueStats 获取收入统计
func (ss *StatService) getRevenueStats(db *gorm.DB, stats *DashboardStats, todayStartMs, monthStartMs int64) error {
	// 今日收入
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ?", "completed", todayStartMs).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TodayRevenue).Error; err != nil {
		return fmt.Errorf("获取今日收入失败: %w", err)
	}

	// 本月收入
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ?", "completed", monthStartMs).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.MonthRevenue).Error; err != nil {
		return fmt.Errorf("获取本月收入失败: %w", err)
	}

	// 总收入
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", "completed").
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TotalRevenue).Error; err != nil {
		return fmt.Errorf("获取总收入失败: %w", err)
	}

	return nil
}

// getUsageStats 获取使用量统计
func (ss *StatService) getUsageStats(db *gorm.DB, stats *DashboardStats, todayStartMs, monthStartMs int64, now time.Time) error {
	// 今日使用量
	if err := db.Model(&model.UsageLog{}).
		Where("created_at >= ?", todayStartMs).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&stats.TodayUsage).Error; err != nil {
		return fmt.Errorf("获取今日使用量失败: %w", err)
	}

	// 本月使用量
	if err := db.Model(&model.UsageLog{}).
		Where("created_at >= ?", monthStartMs).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&stats.MonthUsage).Error; err != nil {
		return fmt.Errorf("获取本月使用量失败: %w", err)
	}

	// 总使用量
	if err := db.Model(&model.UsageLog{}).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&stats.TotalUsage).Error; err != nil {
		return fmt.Errorf("获取总使用量失败: %w", err)
	}

	// 计算平均日使用量（基于本月数据）
	daysInMonth := now.Day()
	if daysInMonth > 0 {
		stats.AvgDailyUsage = stats.MonthUsage / int64(daysInMonth)
	}

	return nil
}

// calculateGrowthRates 计算增长率
func (ss *StatService) calculateGrowthRates(db *gorm.DB, stats *DashboardStats, monthStartMs, lastMonthStartMs int64) error {
	// 上月收入
	var lastMonthRevenue float64
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ? AND created_at >= ? AND created_at < ?", "completed", lastMonthStartMs, monthStartMs).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&lastMonthRevenue).Error; err != nil {
		return fmt.Errorf("获取上月收入失败: %w", err)
	}

	if lastMonthRevenue > 0 {
		stats.RevenueGrowth = ((stats.MonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100
	}

	// 上月新增用户数
	var thisMonthUsers, lastMonthUsers int64
	if err := db.Model(&model.User{}).Where("created_at >= ?", monthStartMs).Count(&thisMonthUsers).Error; err != nil {
		return fmt.Errorf("获取本月新增用户数失败: %w", err)
	}
	if err := db.Model(&model.User{}).Where("created_at >= ? AND created_at < ?", lastMonthStartMs, monthStartMs).Count(&lastMonthUsers).Error; err != nil {
		return fmt.Errorf("获取上月新增用户数失败: %w", err)
	}

	if lastMonthUsers > 0 {
		stats.UserGrowth = ((float64(thisMonthUsers) - float64(lastMonthUsers)) / float64(lastMonthUsers)) * 100
	}

	// 上月使用量
	var lastMonthUsage int64
	if err := db.Model(&model.UsageLog{}).
		Where("created_at >= ? AND created_at < ?", lastMonthStartMs, monthStartMs).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&lastMonthUsage).Error; err != nil {
		return fmt.Errorf("获取上月使用量失败: %w", err)
	}

	if lastMonthUsage > 0 {
		stats.UsageGrowth = ((float64(stats.MonthUsage) - float64(lastMonthUsage)) / float64(lastMonthUsage)) * 100
	}

	// 余额增长率（简化计算，基于本月充值）
	if stats.TotalBalance > 0 && stats.MonthRevenue > 0 {
		stats.BalanceGrowth = (stats.MonthRevenue / stats.TotalBalance) * 100
	}

	return nil
}

// getModuleStats 获取模块统计数据
func (ss *StatService) getModuleStats(db *gorm.DB, monthStartMs int64) ([]ModuleStatsItem, error) {
	var results []ModuleStatsItem

	// 使用GORM的聚合查询替代原生SQL
	if err := db.Model(&model.UsageLog{}).
		Select("module, unit, COALESCE(SUM(`usage`), 0) as `usage`, COALESCE(SUM(`cost`), 0) as revenue, COUNT(DISTINCT api_key) as `keys`").
		Where("created_at >= ?", monthStartMs).
		Group("module, unit").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("查询模块统计数据失败: %w", err)
	}

	// 转换为响应格式
	moduleStats := make([]ModuleStatsItem, 0, len(results))

	for _, result := range results {
		// 计算增长率 - 获取上月同期数据进行对比
		growth := ss.calculateModuleGrowth(db, result.Module, monthStartMs, result.Usage)
		result.Growth = growth
		moduleStats = append(moduleStats, result)
	}

	return moduleStats, nil
}

// calculateModuleGrowth 计算模块增长率
func (ss *StatService) calculateModuleGrowth(db *gorm.DB, module string, monthStartMs int64, currentUsage int64) float64 {
	// 计算上月同期的开始时间
	monthStart := time.UnixMilli(monthStartMs)
	lastMonthStart := monthStart.AddDate(0, -1, 0)
	lastMonthStartMs := lastMonthStart.UnixMilli()

	var lastMonthUsage int64
	if err := db.Model(&model.UsageLog{}).
		Where("module = ? AND created_at >= ? AND created_at < ?", module, lastMonthStartMs, monthStartMs).
		Select("COALESCE(SUM(`usage`), 0)").
		Scan(&lastMonthUsage).Error; err != nil {
		logger.Error("计算模块增长率失败", "module", module, "error", err)
		return 0.0
	}

	if lastMonthUsage > 0 {
		return ((float64(currentUsage) - float64(lastMonthUsage)) / float64(lastMonthUsage)) * 100
	}

	// 如果上月没有数据但本月有数据，返回100%增长
	if currentUsage > 0 {
		return 100.0
	}

	return 0.0
}

// GetRechargeStats 获取充值统计数据
func (ss *StatService) GetRechargeStats(ctx context.Context) (*RechargeStats, error) {
	db := cosy.UseDB(ctx)

	// 获取当前时间
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// 转换为毫秒时间戳
	todayStartMs := todayStart.UnixMilli()
	monthStartMs := monthStart.UnixMilli()

	stats := &RechargeStats{}

	// 今日充值金额
	if err := db.Model(&model.RechargeRecord{}).
		Where("created_at >= ? AND status = ?", todayStartMs, types.RechargeStatusCompleted).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.TodayAmount).Error; err != nil {
		return nil, fmt.Errorf("获取今日充值金额失败: %w", err)
	}

	// 本月充值金额
	if err := db.Model(&model.RechargeRecord{}).
		Where("created_at >= ? AND status = ?", monthStartMs, types.RechargeStatusCompleted).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.MonthAmount).Error; err != nil {
		return nil, fmt.Errorf("获取本月充值金额失败: %w", err)
	}

	// 总充值次数
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusCompleted).
		Count(&stats.TotalCount).Error; err != nil {
		return nil, fmt.Errorf("获取总充值次数失败: %w", err)
	}

	// 平均充值金额
	if stats.TotalCount > 0 {
		var totalAmount float64
		if err := db.Model(&model.RechargeRecord{}).
			Where("status = ?", types.RechargeStatusCompleted).
			Select("COALESCE(SUM(amount), 0)").
			Scan(&totalAmount).Error; err != nil {
			return nil, fmt.Errorf("获取总充值金额失败: %w", err)
		}
		stats.AverageAmount = totalAmount / float64(stats.TotalCount)
	}

	// 各状态金额统计
	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusPending).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.PendingAmount).Error; err != nil {
		return nil, fmt.Errorf("获取待处理金额失败: %w", err)
	}

	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusCompleted).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.CompletedAmount).Error; err != nil {
		return nil, fmt.Errorf("获取已完成金额失败: %w", err)
	}

	if err := db.Model(&model.RechargeRecord{}).
		Where("status = ?", types.RechargeStatusFailed).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&stats.FailedAmount).Error; err != nil {
		return nil, fmt.Errorf("获取失败金额失败: %w", err)
	}

	// 总用户数
	if err := db.Model(&model.User{}).Count(&stats.TotalUsers).Error; err != nil {
		return nil, fmt.Errorf("获取总用户数失败: %w", err)
	}

	// 活跃用户数（有余额的用户）
	if err := db.Model(&model.User{}).
		Where("balance > 0").
		Count(&stats.ActiveUsers).Error; err != nil {
		return nil, fmt.Errorf("获取活跃用户数失败: %w", err)
	}

	// 用户总余额
	if err := db.Model(&model.User{}).
		Select("COALESCE(SUM(balance), 0)").
		Scan(&stats.TotalBalance).Error; err != nil {
		return nil, fmt.Errorf("获取用户总余额失败: %w", err)
	}

	return stats, nil
}

// GetKeyOverviewStats 获取API Key概览统计
func (ss *StatService) GetKeyOverviewStats(ctx context.Context) (*KeyOverviewStats, error) {
	db := cosy.UseDB(ctx)

	stats := &KeyOverviewStats{}

	// 1. 获取基础统计数据
	// 总Key数量
	if err := db.Model(&model.ApiKey{}).Count(&stats.TotalKeys).Error; err != nil {
		return nil, fmt.Errorf("获取总Key数失败: %w", err)
	}

	// 活跃Key数量
	if err := db.Model(&model.ApiKey{}).Where("status = ?", "ok").Count(&stats.ActiveKeys).Error; err != nil {
		return nil, fmt.Errorf("获取活跃Key数失败: %w", err)
	}

	// 阻止Key数量
	if err := db.Model(&model.ApiKey{}).Where("status = ?", "blocked").Count(&stats.BlockedKeys).Error; err != nil {
		return nil, fmt.Errorf("获取阻止Key数失败: %w", err)
	}

	// 2. 有配额的Key数量
	var quotaSubQuery = db.Model(&model.QuotaPackageRecord{}).
		Select("DISTINCT api_key").
		Where("api_key IS NOT NULL AND api_key != '' AND status = 'active'")
	if err := db.Model(&model.ApiKey{}).
		Where("api_key IN (?)", quotaSubQuery).
		Count(&stats.KeysWithQuota).Error; err != nil {
		return nil, fmt.Errorf("获取有配额Key数失败: %w", err)
	}

	// 3. 用户有余额的Key数量
	var balanceSubQuery = db.Model(&model.User{}).
		Select("id").
		Where("balance > 0")
	if err := db.Model(&model.ApiKey{}).
		Where("user_id IN (?)", balanceSubQuery).
		Count(&stats.KeysWithBalance).Error; err != nil {
		return nil, fmt.Errorf("获取用户有余额Key数失败: %w", err)
	}

	// 4. 计算配额使用率统计
	type QuotaUsageResult struct {
		TotalQuota int64   `json:"total_quota"`
		TotalUsed  int64   `json:"total_used"`
		AvgUsage   float64 `json:"avg_usage"`
	}

	var quotaUsage QuotaUsageResult
	if err := db.Model(&model.QuotaPackageRecord{}).
		Select("SUM(quota) as `total_quota`, SUM(used) as `total_used`, AVG(CASE WHEN quota > 0 THEN (used * 100.0 / quota) ELSE 0 END) as `avg_usage`").
		Where("status = 'active'").
		Scan(&quotaUsage).Error; err != nil {
		return nil, fmt.Errorf("计算配额使用率失败: %w", err)
	}

	if quotaUsage.TotalQuota > 0 {
		stats.TotalQuotaUsage = float64(quotaUsage.TotalUsed) * 100.0 / float64(quotaUsage.TotalQuota)
	}
	stats.AvgQuotaUsage = quotaUsage.AvgUsage

	// 5. 按模块统计
	moduleStats, err := ss.getKeyModuleStats(db)
	if err != nil {
		return nil, fmt.Errorf("获取模块统计失败: %w", err)
	}
	stats.ModuleStats = moduleStats

	return stats, nil
}

// getKeyModuleStats 获取Key模块统计
func (ss *StatService) getKeyModuleStats(db *gorm.DB) ([]ModuleStat, error) {
	type ModuleStatsResult struct {
		Module      string  `json:"module"`
		KeyCount    int64   `json:"key_count"`
		ActiveCount int64   `json:"active_count"`
		AvgUsage    float64 `json:"avg_usage"`
	}

	var moduleStatsResults []ModuleStatsResult

	// 使用GORM的聚合查询替代原生SQL
	if err := db.Model(&model.QuotaPackageRecord{}).
		Select("module, COUNT(DISTINCT api_key) as `key_count`, AVG(CASE WHEN quota > 0 THEN (used * 100.0 / quota) ELSE 0 END) as `avg_usage`").
		Where("api_key IS NOT NULL AND api_key != '' AND status = 'active'").
		Group("module").
		Find(&moduleStatsResults).Error; err != nil {
		return nil, fmt.Errorf("查询模块统计数据失败: %w", err)
	}

	// 计算活跃Key数量
	for i := range moduleStatsResults {
		var activeCount int64
		subQuery := db.Model(&model.QuotaPackageRecord{}).
			Select("DISTINCT api_key").
			Where("module = ? AND api_key IS NOT NULL AND api_key != '' AND status = 'active'", moduleStatsResults[i].Module)

		if err := db.Model(&model.ApiKey{}).
			Where("api_key IN (?) AND status = 'ok'", subQuery).
			Count(&activeCount).Error; err != nil {
			logger.Error("计算模块活跃Key数量失败", "module", moduleStatsResults[i].Module, "error", err)
			activeCount = 0
		}
		moduleStatsResults[i].ActiveCount = activeCount
	}

	// 转换为响应格式
	moduleStats := make([]ModuleStat, 0, len(moduleStatsResults))
	moduleNameMap := map[string]string{
		"llm": "LLM服务",
		"tts": "TTS服务",
		"asr": "ASR服务",
	}

	for _, result := range moduleStatsResults {
		name := moduleNameMap[result.Module]
		if name == "" {
			name = result.Module
		}

		moduleStats = append(moduleStats, ModuleStat{
			Module:      result.Module,
			Name:        name,
			KeyCount:    result.KeyCount,
			ActiveCount: result.ActiveCount,
			AvgUsage:    result.AvgUsage,
		})
	}

	return moduleStats, nil
}

// GetUsageStats 获取使用量统计
func (ss *StatService) GetUsageStats(ctx context.Context, apiKey, period string) (*UsageStats, error) {
	db := cosy.UseDB(ctx)

	var startTime time.Time
	now := time.Now()

	switch period {
	case "day":
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	case "week":
		startTime = now.AddDate(0, 0, -7)
	case "month":
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	default:
		startTime = time.Date(2025, 1, 1, 0, 0, 0, 0, now.Location())
	}

	// 查询统计数据，按模块分开统计（不同模块的usage单位不同，不能直接相加）
	var moduleStats []ModuleUsageStatsItem
	query := db.Model(&model.UsageLog{}).
		Select("module, SUM(`usage`) as total_usage, SUM(`cost`) as total_cost, COUNT(*) as `count`, MAX(`unit`) as unit").
		Where("created_at >= ?", startTime.UnixMilli()).
		Group("module")

	if apiKey != "" {
		query = query.Where("api_key = ?", apiKey)
	}

	if err := query.Find(&moduleStats).Error; err != nil {
		return nil, fmt.Errorf("查询使用量统计失败: %w", err)
	}

	// 只计算总成本和总请求数，不计算总usage（因为单位不同）
	var totalCost float64
	var totalCount int64

	for _, stat := range moduleStats {
		totalCost += stat.TotalCost
		totalCount += stat.Count
	}

	stats := &UsageStats{
		Period:     period,
		StartTime:  startTime.UnixMilli(),
		EndTime:    now.UnixMilli(),
		TotalCost:  totalCost,
		TotalCount: totalCount,
		ByModule:   moduleStats,
	}

	return stats, nil
}

// RecentActiveUser 近期活跃用户结构
type RecentActiveUser struct {
	ID         string  `json:"id"`
	Name       string  `json:"name"`
	Email      string  `json:"email"`
	Balance    float64 `json:"balance"`
	Usage      int64   `json:"usage"`
	LastActive string  `json:"last_active"`
}

// RecentRecharge 近期充值记录结构
type RecentRecharge struct {
	ID     string  `json:"id"`
	User   string  `json:"user"`
	Amount float64 `json:"amount"`
	Type   string  `json:"type"`
	Time   string  `json:"time"`
}

// SystemHealthStatus 系统健康状态结构
type SystemHealthStatus struct {
	APIResponseTime   int     `json:"api_response_time"`  // API响应时间(ms)
	ErrorRate         float64 `json:"error_rate"`         // 错误率
	Uptime            float64 `json:"uptime"`             // 系统可用性
	QueueDepth        int     `json:"queue_depth"`        // 队列深度
	ActiveConnections int64   `json:"active_connections"` // 活跃连接数
	LastCheck         string  `json:"last_check"`         // 最后检查时间
}

// GetRecentActiveUsers 获取近期活跃用户
func (ss *StatService) GetRecentActiveUsers(ctx context.Context, limit int) ([]RecentActiveUser, error) {
	db := cosy.UseDB(ctx)

	if limit <= 0 {
		limit = 5
	}

	var users []model.User
	// 获取最近活跃的用户（有余额且最近有活动）
	if err := db.Where("balance > 0").
		Order("last_active DESC").
		Limit(limit).
		Find(&users).Error; err != nil {
		return nil, fmt.Errorf("获取近期活跃用户失败: %w", err)
	}

	recentActiveUsers := make([]RecentActiveUser, 0, len(users))
	for _, user := range users {
		// 使用GORM的子查询计算用户的总使用量
		var totalUsage int64
		subQuery := db.Model(&model.ApiKey{}).
			Select("api_key").
			Where("user_id = ?", user.ID)

		if err := db.Model(&model.UsageLog{}).
			Where("api_key IN (?)", subQuery).
			Select("COALESCE(SUM(`usage`), 0)").
			Scan(&totalUsage).Error; err != nil {
			logger.Error("计算用户使用量失败", "user_id", user.ID, "error", err)
			totalUsage = 0
		}

		// 格式化最后活跃时间
		lastActive := ss.formatLastActiveTime(user.LastActive)

		recentActiveUsers = append(recentActiveUsers, RecentActiveUser{
			ID:         fmt.Sprintf("%d", user.ID),
			Name:       user.Name,
			Email:      user.Email,
			Balance:    user.Balance,
			Usage:      totalUsage,
			LastActive: lastActive,
		})
	}

	return recentActiveUsers, nil
}

// GetRecentRecharges 获取近期充值记录
func (ss *StatService) GetRecentRecharges(ctx context.Context, limit int) ([]RecentRecharge, error) {
	db := cosy.UseDB(ctx)

	if limit <= 0 {
		limit = 5
	}

	var records []model.RechargeRecord
	// 获取最近的充值记录
	if err := db.Preload("User").
		Where("status = ?", "completed").
		Order("created_at DESC").
		Limit(limit).
		Find(&records).Error; err != nil {
		return nil, fmt.Errorf("获取近期充值记录失败: %w", err)
	}

	recentRecharges := make([]RecentRecharge, 0, len(records))
	for _, record := range records {
		userName := "未知用户"
		if record.User != nil {
			userName = record.User.Name
		}

		// 格式化时间
		timeStr := ss.formatRechargeTime(record.CreatedAt)

		recentRecharges = append(recentRecharges, RecentRecharge{
			ID:     fmt.Sprintf("%d", record.ID),
			User:   userName,
			Amount: record.Amount,
			Type:   record.Type,
			Time:   timeStr,
		})
	}

	return recentRecharges, nil
}

// GetSystemHealth 获取系统健康状态
func (ss *StatService) GetSystemHealth(ctx context.Context) SystemHealthStatus {
	// todo
	// 这里可以添加真实的系统监控逻辑
	// 目前返回模拟数据，后续可以集成真实的监控系统
	return SystemHealthStatus{
		APIResponseTime:   125,
		ErrorRate:         0.02,
		Uptime:            99.9,
		QueueDepth:        3,
		ActiveConnections: 1247,
		LastCheck:         time.Now().Format(time.RFC3339),
	}
}

// formatLastActiveTime 格式化最后活跃时间
func (ss *StatService) formatLastActiveTime(lastActive int64) string {
	if lastActive <= 0 {
		return "未知"
	}

	lastActiveTime := time.Unix(lastActive, 0)
	now := time.Now()
	diff := now.Sub(lastActiveTime)

	if diff < time.Minute {
		return "刚刚"
	} else if diff < time.Hour {
		return fmt.Sprintf("%d分钟前", int(diff.Minutes()))
	} else if diff < 24*time.Hour {
		return fmt.Sprintf("%d小时前", int(diff.Hours()))
	} else {
		return fmt.Sprintf("%d天前", int(diff.Hours()/24))
	}
}

// formatRechargeTime 格式化充值时间
func (ss *StatService) formatRechargeTime(createdAt int64) string {
	createdTime := time.UnixMilli(createdAt)
	now := time.Now()
	diff := now.Sub(createdTime)

	if diff < time.Minute {
		return "刚刚"
	} else if diff < time.Hour {
		return fmt.Sprintf("%d分钟前", int(diff.Minutes()))
	} else if diff < 24*time.Hour {
		return fmt.Sprintf("%d小时前", int(diff.Hours()))
	} else {
		return fmt.Sprintf("%d天前", int(diff.Hours()/24))
	}
}
