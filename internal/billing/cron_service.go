package billing

import (
	"sync"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/go-co-op/gocron/v2"
	"github.com/uozi-tech/cosy/logger"
)

// CronService 定时任务服务
type CronService struct {
	billingService *BillingService
	scheduler      gocron.Scheduler
}

var (
	cronServiceInstance *CronService
	cronServiceOnce     sync.Once
)

// GetCronService 获取定时任务服务单例
func GetCronService() *CronService {
	cronServiceOnce.Do(func() {
		billingService := GetBillingService()

		// 创建调度器
		scheduler, err := gocron.NewScheduler()
		if err != nil {
			logger.Error("创建定时任务调度器失败", "error", err)
			return
		}

		cronServiceInstance = &CronService{
			billingService: billingService,
			scheduler:      scheduler,
		}
		logger.Info("CronService singleton initialized")
	})
	return cronServiceInstance
}

// CheckOverdueKeys 检查欠费的key并停止服务
func (c *CronService) CheckOverdueKeys() {
	logger.Info("开始检查欠费的key...")

	// 查询所有状态为 ok 的 key
	apiKeys, err := query.ApiKey.
		Where(query.ApiKey.Status.Eq(types.KeyStatusOk)).
		Find()
	if err != nil {
		logger.Error("查询billing keys失败", "error", err)
		return
	}

	// 收集需要阻止的keys
	var keysToBlock []KeyStatusUpdate

	for _, key := range apiKeys {
		if c.shouldBlockKey(*key) {
			keysToBlock = append(keysToBlock, KeyStatusUpdate{
				Key:    key.APIKey,
				Status: types.KeyStatusBlocked,
				Reason: "Quota exhausted or expired",
			})
		}
	}

	if len(keysToBlock) == 0 {
		logger.Info("Overdue key check completed", "total", len(apiKeys), "stopped", 0)
		return
	}

	// 使用批量更新方法
	if err := c.billingService.GetKeyService().UpdateKeyStatusBatch(keysToBlock); err != nil {
		logger.Error("Failed to update overdue key status", "error", err)
		return
	}

	logger.Info("Overdue key check completed", "total", len(apiKeys), "stopped", len(keysToBlock))
}

// shouldBlockKey 检查key是否应该被阻止
func (c *CronService) shouldBlockKey(key model.ApiKey) bool {
	// 获取用户信息
	var user *model.User
	if key.UserID != 0 {
		// 查询用户信息
		foundUser, err := query.User.Where(query.User.ID.Eq(key.UserID)).First()
		if err != nil {
			logger.Error("Failed to query user", "error", err, "user_id", key.UserID)
			// 如果无法获取用户信息，为安全起见阻止key
			return true
		}
		user = foundUser
	}

	var quotas []*model.QuotaPackageRecord
	err := query.QuotaPackageRecord.UnderlyingDB().
		Where(
			"(api_key IN (?) OR api_key IS NULL)",
			[]string{key.APIKey, ""},
		).
		Where("status = ?", types.QuotaPackageStatusActive).
		Find(&quotas).
		Error

	if err != nil {
		logger.Error("Failed to query key quota", "error", err)
		return false
	}

	now := time.Now()
	hasValidQuota := false

	// 检查是否有有效的配额（只检查对应类型的配额）
	if len(quotas) > 0 {
		for _, quota := range quotas {
			// 检查配额是否过期
			if quota.ExpiresAt != 0 && quota.ExpiresAt < now.UnixMilli() {
				logger.Debug("Key quota expired", "key", key.APIKey, "module", quota.Module, "expires_at", quota.ExpiresAt)
				continue
			}

			// 检查配额是否用尽
			if quota.Quota > 0 && quota.Used >= quota.Quota {
				logger.Debug("Key quota exhausted", "key", key.APIKey, "module", quota.Module, "used", quota.Used, "quota", quota.Quota)
				continue
			}

			// 如果有至少一个有效配额，不需要检查余额
			hasValidQuota = true
			break
		}

		// 如果该类型的所有配额都无效（过期或用尽），且用户余额不足，则阻止key
		if !hasValidQuota {
			if user == nil || user.Balance <= 0 {
				logger.Debug("Key blocked: no valid quota for type and insufficient balance",
					"key", key.APIKey,
					"user_id", key.UserID,
					"balance", func() float64 {
						if user != nil {
							return user.Balance
						}
						return 0
					}())
				return true
			}
		}
	} else {
		// 没有该类型的配额设置，检查用户余额
		if user == nil || user.Balance <= 0 {
			logger.Debug("Key blocked: no quota for type and insufficient balance",
				"key", key.APIKey,
				"user_id", key.UserID,
				"balance", func() float64 {
					if user != nil {
						return user.Balance
					}
					return 0
				}())
			return true
		}
	}

	return false
}

// CheckExpiredKeys 检查过期的key
func (c *CronService) CheckExpiredKeys() {
	logger.Info("开始检查过期的key...")

	// 查询所有状态为 ok 的 key
	apiKeys, err := query.ApiKey.
		Where(query.ApiKey.Status.Eq(types.KeyStatusOk)).
		Find()
	if err != nil {
		logger.Error("查询API keys失败", "error", err)
		return
	}

	// 收集需要阻止的keys
	var keysToBlock []KeyStatusUpdate

	for _, key := range apiKeys {
		if c.shouldBlockKey(*key) {
			keysToBlock = append(keysToBlock, KeyStatusUpdate{
				Key:    key.APIKey,
				Status: types.KeyStatusBlocked,
				Reason: "Quota expired and insufficient balance",
			})
		}
	}

	if len(keysToBlock) == 0 {
		logger.Info("Expired key check completed", "total", len(apiKeys), "stopped", 0)
		return
	}

	// 使用批量更新方法
	if err := c.billingService.GetKeyService().UpdateKeyStatusBatch(keysToBlock); err != nil {
		logger.Error("Failed to update expired key status", "error", err)
		return
	}

	logger.Info("Expired key check completed", "total", len(apiKeys), "stopped", len(keysToBlock))
}

// RegisterCronJobs 注册定时任务
func RegisterCronJobs() {
	logger.Info("Registering billing system cron jobs...")

	// 获取定时任务服务单例
	cronService := GetCronService()
	if cronService == nil {
		logger.Error("Failed to get cron service instance, skipping cron job registration")
		return
	}

	// 注册定时任务

	// 每5秒检查一次欠费的key（开发测试用，生产环境建议改为5分钟）
	_, err := cronService.scheduler.NewJob(
		gocron.DurationJob(time.Minute*5),
		gocron.NewTask(func() {
			cronService.CheckOverdueKeys()
		}),
	)
	if err != nil {
		logger.Error("Failed to register overdue key check cron job", "error", err)
	} else {
		logger.Info("Overdue key check cron job registered - every 5 minutes")
	}

	// 每10分钟检查一次过期的key
	_, err = cronService.scheduler.NewJob(
		gocron.DurationJob(time.Minute*10),
		gocron.NewTask(func() {
			cronService.CheckExpiredKeys()
		}),
	)
	if err != nil {
		logger.Error("Failed to register expired key check cron job", "error", err)
	} else {
		logger.Info("Expired key check cron job registered - every 10 minutes")
	}

	// 启动调度器
	cronService.scheduler.Start()
	logger.Info("Cron jobs scheduler started")
}

// StopCronJobs 停止定时任务
func StopCronJobs() {
	cronService := GetCronService()
	if cronService != nil && cronService.scheduler != nil {
		if err := cronService.scheduler.Shutdown(); err != nil {
			logger.Error("Failed to stop cron jobs scheduler", "error", err)
		} else {
			logger.Info("Cron jobs scheduler stopped")
		}
	}
}
