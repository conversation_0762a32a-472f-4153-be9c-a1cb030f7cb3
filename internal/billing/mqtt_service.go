package billing

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"strconv"
	"time"

	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/eclipse/paho.golang/autopaho"
	"github.com/eclipse/paho.golang/paho"
	"github.com/uozi-tech/cosy/logger"
)

// MQTTService MQTT服务结构体 - 仅负责消息通信
type MQTTService struct {
	client        *autopaho.ConnectionManager
	ctx           context.Context
	billingEngine *BillingEngine // 业务逻辑委托给计费引擎
}

// UsageReport 用量上报消息结构
type UsageReport struct {
	APIKey    string                 `json:"api_key"`   // API Key
	Module    string                 `json:"module"`    // 'llm' | 'tts' | 'asr'
	Model     string                 `json:"model"`     // 模型名称
	Usage     int64                  `json:"usage"`     // 用量
	Metadata  map[string]interface{} `json:"metadata"`  // 元数据
	Timestamp int64                  `json:"timestamp"` // 使用时间戳
}

// KeyStatusUpdate Key状态更新消息
type KeyStatusUpdate struct {
	Key    string `json:"key"`
	Status string `json:"status"` // 'ok' | 'blocked'
	Reason string `json:"reason"` // 状态更新原因
}

// KeyStatusBatchUpdate 批量Key状态更新消息
type KeyStatusBatchUpdate struct {
	Updates   []KeyStatusUpdate `json:"updates"`
	Timestamp int64             `json:"timestamp"`
}

// NewMQTTService 创建新的MQTT服务
func NewMQTTService(ctx context.Context, brokerURL string) (*MQTTService, error) {
	u, err := url.Parse(brokerURL)
	if err != nil {
		return nil, fmt.Errorf("解析MQTT broker URL失败: %w", err)
	}

	service := &MQTTService{
		ctx: ctx,
	}

	clientID := generateClientID()
	logger.Info("Creating MQTT client", "client_id", clientID)

	cliCfg := autopaho.ClientConfig{
		ServerUrls:                    []*url.URL{u},
		KeepAlive:                     30,
		CleanStartOnInitialConnection: false, // 不清除旧会话
		SessionExpiryInterval:         7200,  // 设置会话过期时间为 2 小时，断线后 2 小时内消息都能缓存

		TlsCfg: &tls.Config{
			InsecureSkipVerify: true,
		},

		ConnectUsername: settings.Billing.MQTTUsername,
		ConnectPassword: []byte(settings.Billing.MQTTPassword),

		OnConnectionUp: func(cm *autopaho.ConnectionManager, connAck *paho.Connack) {
			logger.Info("MQTT connection established")

			// 订阅用量上报主题
			if _, err := cm.Subscribe(context.Background(), &paho.Subscribe{
				Subscriptions: []paho.SubscribeOptions{
					{Topic: "billing/report", QoS: 1},
					{Topic: "billing/keys/request", QoS: 1},
				},
			}); err != nil {
				logger.Error("Failed to subscribe to topics", "error", err)
			} else {
				logger.Info("Successfully subscribed to topics")
			}
		},

		OnConnectError: func(err error) {
			logger.Error("MQTT connection error", "error", err)
		},

		ClientConfig: paho.ClientConfig{
			ClientID: clientID,
			OnPublishReceived: []func(paho.PublishReceived) (bool, error){
				service.handleMessage,
			},
			OnClientError: func(err error) {
				logger.Error("MQTT client error", "error", err)
			},
			OnServerDisconnect: func(d *paho.Disconnect) {
				if d.Properties != nil {
					logger.Warn("Server requested disconnect", "reason", d.Properties.ReasonString)
				} else {
					logger.Warn("Server requested disconnect", "reason_code", d.ReasonCode)
				}
			},
		},
	}

	client, err := autopaho.NewConnection(context.Background(), cliCfg)
	if err != nil {
		return nil, fmt.Errorf("Create MQTT connection failed: %w", err)
	}

	service.client = client
	return service, nil
}

// SetBillingEngine 设置计费引擎（用于依赖注入）
func (s *MQTTService) SetBillingEngine(billingEngine *BillingEngine) {
	s.billingEngine = billingEngine
}

// handleMessage 处理接收到的MQTT消息
func (s *MQTTService) handleMessage(pr paho.PublishReceived) (bool, error) {
	topic := pr.Packet.Topic
	payload := pr.Packet.Payload

	logger.Info("Received MQTT message", "topic", topic, "payload_size", len(payload))

	switch topic {
	case "billing/report":
		return s.handleUsageReport(payload)
	case "billing/keys/request":
		return s.handleKeyRequest(payload)
	default:
		logger.Warn("Received message on unknown topic", "topic", topic)
	}

	return true, nil
}

// handleUsageReport 处理用量上报消息 - 仅负责消息解析和委托
func (s *MQTTService) handleUsageReport(payload []byte) (bool, error) {
	var report UsageReport
	if err := json.Unmarshal(payload, &report); err != nil {
		logger.Error("Failed to parse usage report", "error", err, "payload", string(payload))
		return true, fmt.Errorf("Failed to parse usage report: %w", err)
	}

	// 验证消息格式
	if report.APIKey == "" || report.Module == "" || report.Usage <= 0 {
		logger.Errorf("Invalid usage report(api_key: %s, module: %s, usage: %d)", report.APIKey, report.Module, report.Usage)
		return true, fmt.Errorf("Invalid usage report format")
	}

	// 将业务逻辑委托给计费引擎
	if s.billingEngine != nil {
		billingRequest := types.BillingRequest{
			APIKey:    report.APIKey,
			Module:    report.Module,
			ModelName: report.Model,
			Usage:     report.Usage,
			Metadata:  report.Metadata,
		}

		result, err := s.billingEngine.ProcessBilling(billingRequest)
		if err != nil {
			logger.Error("Failed to process billing", "error", err, "report", report)
			// 不返回错误，避免影响消息确认
		} else {
			logger.Info("Usage billing processed successfully",
				"api_key", report.APIKey,
				"module", report.Module,
				"cost", result.Cost,
				"billing_type", result.BillingType)
		}
	} else {
		logger.Warn("BillingEngine not set, skipping billing processing")
	}

	return true, nil
}

// handleKeyRequest 处理Key请求消息, 通过 "billing/key/update" 发布完整的 key 列表
func (s *MQTTService) handleKeyRequest(payload []byte) (bool, error) {
	ok, err := s.handleKeyUpdate(payload)
	if err != nil {
		logger.Error("Failed to handle key request", "error", err)
		return ok, fmt.Errorf("Failed to handle key request: %w", err)
	}
	return ok, nil
}

// handleKeyUpdate 处理Key更新消息
func (s *MQTTService) handleKeyUpdate(payload []byte) (bool, error) {
	keys, err := query.ApiKey.Where(query.ApiKey.Status.In(types.KeyStatusOk, types.KeyStatusBlocked)).Find()
	if err != nil {
		logger.Error("Failed to get key list", "error", err)
		return true, fmt.Errorf("Failed to get key list: %w", err)
	}

	updates := make([]KeyStatusUpdate, 0, len(keys))
	for _, key := range keys {
		updates = append(updates, KeyStatusUpdate{
			Key:    key.APIKey,
			Status: key.Status,
			Reason: "",
		})
	}

	s.PublishKeyStatusBatch(updates)

	return true, nil
}

// PublishKeyStatus 发布Key状态更新（单个）
func (s *MQTTService) PublishKeyStatus(key, status, reason string) {
	updates := []KeyStatusUpdate{
		{
			Key:    key,
			Status: status,
			Reason: reason,
		},
	}
	s.PublishKeyStatusBatch(updates)
}

// PublishKeyStatusBatch 批量发布Key状态更新
func (s *MQTTService) PublishKeyStatusBatch(updates []KeyStatusUpdate) {
	if len(updates) == 0 {
		return
	}

	topic := "billing/keys/update"
	message := KeyStatusBatchUpdate{
		Updates:   updates,
		Timestamp: time.Now().UnixMilli(),
	}

	payload, err := json.Marshal(message)
	if err != nil {
		logger.Error("Failed to marshal key status batch message", "error", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err = s.client.Publish(ctx, &paho.Publish{
		QoS:     1,
		Topic:   topic,
		Payload: payload,
	})

	if err != nil {
		logger.Error("Failed to publish key status batch message", "error", err, "topic", topic)
	} else {
		logger.Info("Key status batch update published successfully",
			"topic", topic,
			"count", len(updates),
		)
	}
}

// AwaitConnection 等待MQTT连接建立
func (s *MQTTService) AwaitConnection(ctx context.Context) error {
	return s.client.AwaitConnection(ctx)
}

// Disconnect 断开MQTT连接
func (s *MQTTService) Disconnect(ctx context.Context) error {
	return s.client.Disconnect(ctx)
}

// generateClientID 生成唯一的客户端ID
func generateClientID() string {
	hostname, _ := os.Hostname()
	return "billing-system-" + hostname + "-" + strconv.FormatInt(time.Now().Unix(), 10)
}
