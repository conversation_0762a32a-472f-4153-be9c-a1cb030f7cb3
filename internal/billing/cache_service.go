package billing

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/redis/go-redis/v9"
	"github.com/uozi-tech/cosy/logger"
	cosyRedis "github.com/uozi-tech/cosy/redis"
)

// CacheService Redis缓存服务
type CacheService struct {
	client *redis.Client
}

var (
	cacheServiceInstance *CacheService
	cacheServiceOnce     sync.Once
)

// CachedKeyStatus 缓存的Key状态信息
type CachedKeyStatus struct {
	Key       string                      `json:"key"`
	Status    string                      `json:"status"`
	Available bool                        `json:"available"`
	Quotas    []*model.QuotaPackageRecord `json:"quotas"`
	UpdatedAt int64                       `json:"updated_at"`
}

// GetCacheService 获取缓存服务单例
func GetCacheService() *CacheService {
	cacheServiceOnce.Do(func() {
		redisClient := cosyRedis.GetClient()
		cacheServiceInstance = &CacheService{
			client: redisClient,
		}
		logger.Info("CacheService singleton initialized")
	})
	return cacheServiceInstance
}

// GetKeyStatus 从缓存获取Key状态
func (c *CacheService) GetKeyStatus(key string) (*CachedKeyStatus, error) {
	cacheKey := c.buildKeyStatusCacheKey(key)

	result, err := c.client.Get(context.Background(), cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		logger.Error("Failed to get key status from Redis", "error", err, "key", key)
		return nil, err
	}

	var status CachedKeyStatus
	if err := json.Unmarshal([]byte(result), &status); err != nil {
		logger.Error("Failed to unmarshal key status", "error", err, "key", key)
		return nil, err
	}

	return &status, nil
}

// SetKeyStatus 设置Key状态到缓存
func (c *CacheService) SetKeyStatus(status *CachedKeyStatus, ttl time.Duration) error {
	cacheKey := c.buildKeyStatusCacheKey(status.Key)

	data, err := json.Marshal(status)
	if err != nil {
		logger.Error("Failed to marshal key status", "error", err, "key", status.Key)
		return err
	}

	err = c.client.Set(context.Background(), cacheKey, data, ttl).Err()
	if err != nil {
		logger.Error("Failed to set key status to Redis", "error", err, "key", status.Key)
		return err
	}

	logger.Debug("Key status cached successfully", "key", status.Key, "ttl", ttl)
	return nil
}

// InvalidateKeyStatus 使Key状态缓存失效
func (c *CacheService) InvalidateKeyStatus(key string) error {
	cacheKey := c.buildKeyStatusCacheKey(key)

	err := c.client.Del(context.Background(), cacheKey).Err()
	if err != nil {
		logger.Error("Failed to delete key status cache", "error", err, "key", key)
		return err
	}

	logger.Debug("Key status cache deleted successfully", "key", key)
	return nil
}

// GetQuotaUsage 获取配额使用情况
func (c *CacheService) GetQuotaUsage(ctx context.Context, keyID uint64, module string) (int64, error) {
	cacheKey := c.buildQuotaUsageCacheKey(keyID, module)

	result, err := c.client.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return 0, nil // 缓存未命中，返回0
		}
		logger.Error("Failed to get quota usage from Redis", "error", err, "key_id", keyID, "module", module)
		return 0, err
	}

	var used int64
	if err := json.Unmarshal([]byte(result), &used); err != nil {
		logger.Error("Failed to unmarshal quota usage", "error", err, "key_id", keyID, "module", module)
		return 0, err
	}

	return used, nil
}

// IncrQuotaUsage 增加配额使用量
func (c *CacheService) IncrQuotaUsage(ctx context.Context, keyID uint64, module string, tokens int64, ttl time.Duration) (int64, error) {
	cacheKey := c.buildQuotaUsageCacheKey(keyID, module)

	result := c.client.IncrBy(ctx, cacheKey, tokens)
	if err := result.Err(); err != nil {
		logger.Error("Failed to increment quota usage", "error", err, "key_id", keyID, "module", module, "tokens", tokens)
		return 0, err
	}

	// 设置过期时间
	c.client.Expire(ctx, cacheKey, ttl)

	newUsed := result.Val()
	logger.Debug("Quota usage incremented successfully", "key_id", keyID, "module", module, "tokens", tokens, "new_used", newUsed)

	return newUsed, nil
}

// SetQuotaUsage 设置配额使用量
func (c *CacheService) SetQuotaUsage(ctx context.Context, keyID uint64, module string, used int64, ttl time.Duration) error {
	cacheKey := c.buildQuotaUsageCacheKey(keyID, module)

	err := c.client.Set(ctx, cacheKey, used, ttl).Err()
	if err != nil {
		logger.Error("Failed to set quota usage to Redis", "error", err, "key_id", keyID, "module", module, "used", used)
		return err
	}

	logger.Debug("Quota usage set successfully", "key_id", keyID, "module", module, "used", used)
	return nil
}

// InvalidateQuotaUsage 使配额使用量缓存失效
func (c *CacheService) InvalidateQuotaUsage(keyID uint64, module string) error {
	cacheKey := c.buildQuotaUsageCacheKey(keyID, module)

	err := c.client.Del(context.Background(), cacheKey).Err()
	if err != nil {
		logger.Error("Failed to delete quota usage cache", "error", err, "key_id", keyID, "module", module)
		return err
	}

	logger.Debug("Quota usage cache deleted successfully", "key_id", keyID, "module", module)
	return nil
}

// GetPricingRules 获取缓存的价格规则
func (c *CacheService) GetPricingRules(module, modelName string) (*model.PricingRule, error) {
	cacheKey := c.buildPricingRuleCacheKey(module, modelName)

	result, err := c.client.Get(context.Background(), cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		logger.Error("Failed to get pricing rules from Redis", "error", err, "module", module, "model", modelName)
		return nil, err
	}

	var rule model.PricingRule
	if err := json.Unmarshal([]byte(result), &rule); err != nil {
		logger.Error("Failed to unmarshal pricing rules", "error", err, "module", module, "model", modelName)
		return nil, err
	}

	return &rule, nil
}

// SetPricingRules 设置价格规则到缓存
func (c *CacheService) SetPricingRules(rule *model.PricingRule, ttl time.Duration) error {
	cacheKey := c.buildPricingRuleCacheKey(rule.Module, rule.ModelName)

	data, err := json.Marshal(rule)
	if err != nil {
		logger.Error("Failed to marshal pricing rules", "error", err, "module", rule.Module, "model", rule.ModelName)
		return err
	}

	err = c.client.Set(context.Background(), cacheKey, data, ttl).Err()
	if err != nil {
		logger.Error("Failed to set pricing rules to Redis", "error", err, "module", rule.Module, "model", rule.ModelName)
		return err
	}

	logger.Debug("Pricing rules cached successfully", "module", rule.Module, "model", rule.ModelName, "ttl", ttl)
	return nil
}

// InvalidatePricingRules 使价格规则缓存失效
func (c *CacheService) InvalidatePricingRules(module, modelName string) error {
	cacheKey := c.buildPricingRuleCacheKey(module, modelName)

	err := c.client.Del(context.Background(), cacheKey).Err()
	if err != nil {
		logger.Error("Failed to delete pricing rules cache", "error", err, "module", module, "model", modelName)
		return err
	}

	logger.Debug("Pricing rules cache deleted successfully", "module", module, "model", modelName)
	return nil
}

// InvalidateAllKeyRelated 清除与指定Key相关的所有缓存
func (c *CacheService) InvalidateAllKeyRelated(ctx context.Context, key string, keyID uint64) error {
	// 清除Key状态缓存
	if err := c.InvalidateKeyStatus(key); err != nil {
		logger.Error("Failed to invalidate key status cache", "error", err, "key", key)
	}

	// 清除所有模块的配额使用量缓存
	modules := []string{"llm", "tts", "asr"} // 支持的模块类型
	for _, module := range modules {
		if err := c.InvalidateQuotaUsage(keyID, module); err != nil {
			logger.Error("Failed to invalidate quota usage cache", "error", err, "key_id", keyID, "module", module)
		}
	}

	logger.Info("Key related cache invalidation completed", "key", key, "key_id", keyID)
	return nil
}

// buildKeyStatusCacheKey 构建Key状态缓存键
func (c *CacheService) buildKeyStatusCacheKey(key string) string {
	return fmt.Sprintf("billing:key_status:%s", key)
}

// buildQuotaUsageCacheKey 构建配额使用量缓存键
func (c *CacheService) buildQuotaUsageCacheKey(keyID uint64, module string) string {
	return fmt.Sprintf("billing:quota_usage:%d:%s", keyID, module)
}

// buildPricingRuleCacheKey 构建价格规则缓存键
func (c *CacheService) buildPricingRuleCacheKey(module, modelName string) string {
	if modelName == "" {
		return fmt.Sprintf("billing:pricing_rule:%s:default", module)
	}
	return fmt.Sprintf("billing:pricing_rule:%s:%s", module, modelName)
}

// CheckConnection 检查Redis连接
func (c *CacheService) CheckConnection(ctx context.Context) error {
	_, err := c.client.Ping(ctx).Result()
	if err != nil {
		logger.Error("Redis connection check failed", "error", err)
		return err
	}
	return nil
}
