package billing

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"git.uozi.org/uozi/potato-billing-api/settings"
	"github.com/uozi-tech/cosy/logger"
)

// BillingService 计费系统主服务
type BillingService struct {
	mqttService    *MQTTService
	cacheService   *CacheService
	keyService     *KeyService
	pricingService *PricingService
	quotaService   *QuotaService
	statService    *StatService
	billingEngine  *BillingEngine
	config         *settings.BillingConfig
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
}

var (
	billingServiceInstance *BillingService
	billingServiceOnce     sync.Once
)

// GetBillingService 获取计费服务单例
func GetBillingService() *BillingService {
	billingServiceOnce.Do(func() {
		ctx, cancel := context.WithCancel(context.Background())

		// 获取缓存服务单例
		cacheService := GetCacheService()

		// 创建MQTT服务
		mqttService, err := NewMQTTService(ctx, settings.Billing.MQTTBrokerURL)
		if err != nil {
			logger.Error("创建MQTT服务失败", "error", err)
			cancel()
			return
		}

		// 创建各种服务
		pricingService := NewPricingService(cacheService, settings.Billing)
		quotaService := NewQuotaService(cacheService, settings.Billing)
		statService := NewStatService(cacheService, settings.Billing)
		keyService := NewKeyService(cacheService, mqttService, settings.Billing)
		billingEngine := NewBillingEngine(pricingService, quotaService, keyService)

		// 设置MQTT服务的计费引擎
		mqttService.SetBillingEngine(billingEngine)

		billingServiceInstance = &BillingService{
			mqttService:    mqttService,
			cacheService:   cacheService,
			keyService:     keyService,
			pricingService: pricingService,
			quotaService:   quotaService,
			statService:    statService,
			billingEngine:  billingEngine,
			config:         settings.Billing,
			ctx:            ctx,
			cancel:         cancel,
		}
		logger.Info("BillingService singleton initialized")
	})
	return billingServiceInstance
}

// Start 启动计费服务
func (b *BillingService) Start() error {
	logger.Info("Starting billing service...")

	// 等待MQTT连接建立
	if err := b.mqttService.AwaitConnection(b.ctx); err != nil {
		return fmt.Errorf("MQTT连接失败: %w", err)
	}
	logger.Info("MQTT service started")

	// 启动价格规则缓存预热
	b.wg.Add(1)
	go func() {
		defer b.wg.Done()
		b.preloadPricingRules()
	}()

	// 启动缓存清理任务
	b.wg.Add(1)
	go func() {
		defer b.wg.Done()
		b.runCacheCleanupTask()
	}()

	logger.Info("Billing service started successfully")
	return nil
}

// Stop 停止计费服务
func (b *BillingService) Stop() error {
	logger.Info("Stopping billing service...")

	// 取消上下文
	b.cancel()

	// 断开MQTT连接
	if err := b.mqttService.Disconnect(context.Background()); err != nil {
		logger.Error("Failed to disconnect MQTT connection", "error", err)
	}

	// 等待所有goroutine结束
	b.wg.Wait()

	logger.Info("Billing service stopped")
	return nil
}

// GetCacheService 获取缓存服务
func (b *BillingService) GetCacheService() *CacheService {
	return b.cacheService
}

// GetMQTTService 获取MQTT服务
func (b *BillingService) GetMQTTService() *MQTTService {
	return b.mqttService
}

// GetKeyService 获取Key管理服务
func (b *BillingService) GetKeyService() *KeyService {
	return b.keyService
}

// GetPricingService 获取价格规则服务
func (b *BillingService) GetPricingService() *PricingService {
	return b.pricingService
}

// GetQuotaService 获取资源包管理服务
func (b *BillingService) GetQuotaService() *QuotaService {
	return b.quotaService
}

// GetBillingEngine 获取计费引擎
func (b *BillingService) GetBillingEngine() *BillingEngine {
	return b.billingEngine
}

// preloadPricingRules 预加载价格规则到缓存
func (b *BillingService) preloadPricingRules() {
	b.pricingService.PreloadPricingRules()
}

// runCacheCleanupTask 运行缓存清理任务
func (b *BillingService) runCacheCleanupTask() {
	logger.Info("Starting cache cleanup task")

	ticker := time.NewTicker(time.Hour) // 每小时清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			b.cleanupExpiredCaches()
		case <-b.ctx.Done():
			logger.Info("Cache cleanup task stopped")
			return
		}
	}
}

// cleanupExpiredCaches 清理过期缓存
func (b *BillingService) cleanupExpiredCaches() {
	logger.Debug("Starting to cleanup expired caches")

	// 这里可以添加具体的缓存清理逻辑
	// 例如清理过期的Key状态缓存、配额使用量缓存等

	// 检查Redis连接状态
	if err := b.cacheService.CheckConnection(b.ctx); err != nil {
		logger.Error("Redis connection check failed", "error", err)
	}

	logger.Debug("Cache cleanup completed")
}

// RunBillingServer 运行服务器（包含优雅关闭）
func RunBillingServer() error {
	// 获取计费服务单例
	service := GetBillingService()
	if service == nil {
		return fmt.Errorf("Failed to get billing service")
	}

	// 启动服务
	if err := service.Start(); err != nil {
		return fmt.Errorf("启动计费服务失败: %w", err)
	}

	RegisterCronJobs()

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)

	// 等待信号
	logger.Info("Billing service is running, press Ctrl+C to exit")
	<-sigChan

	// 优雅关闭
	logger.Info("Received shutdown signal, stopping service...")
	return service.Stop()
}

// GetStatService 获取统计服务
func (b *BillingService) GetStatService() *StatService {
	return b.statService
}

// GetStatServiceInstance 获取统计服务单例
func GetStatServiceInstance() *StatService {
	service := GetBillingService()
	if service == nil {
		return nil
	}
	return service.GetStatService()
}
