package billing

import (
	"context"
	"fmt"
	"time"

	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"git.uozi.org/uozi/potato-billing-api/types"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
)

// KeyService Key状态管理服务
type KeyService struct {
	cacheService *CacheService
	mqttService  *MQTTService
	config       *settings.BillingConfig
}

// NewKeyService 创建Key状态管理服务
func NewKeyService(cacheService *CacheService, mqttService *MQTTService, config *settings.BillingConfig) *KeyService {
	return &KeyService{
		cacheService: cacheService,
		mqttService:  mqttService,
		config:       config,
	}
}

// GetKeyStatusWithCache 带缓存的Key状态查询
func (ks *KeyService) GetKeyStatusWithCache(key string) (*CachedKeyStatus, error) {
	// 先从缓存获取
	status, err := ks.cacheService.GetKeyStatus(key)
	if err != nil {
		logger.Error("Failed to get key status from cache", "error", err, "key", key)
	}

	if status != nil {
		// 检查缓存是否过期
		if time.Now().Unix()-status.UpdatedAt < int64(ks.config.CacheTTL.Seconds()) {
			return status, nil
		}
	}

	// 从数据库获取
	billingKey, err := query.ApiKey.
		Preload(query.ApiKey.User).
		Where(query.ApiKey.APIKey.Eq(key)).
		First()
	if err != nil {
		return nil, err
	}

	quotas, err := query.QuotaPackageRecord.
		Where(query.QuotaPackageRecord.APIKey.Eq(billingKey.APIKey)).
		Find()
	if err != nil {
		return nil, err
	}

	// 计算是否可用
	available := billingKey.Status == types.KeyStatusOk
	for _, quota := range quotas {
		if quota.Quota > 0 && quota.Used >= quota.Quota {
			available = false
			break
		}
	}

	// 构建缓存对象
	cachedStatus := &CachedKeyStatus{
		Key:       billingKey.APIKey,
		Status:    billingKey.Status,
		Available: available,
		Quotas:    quotas,
		UpdatedAt: time.Now().Unix(),
	}

	// 更新缓存
	if err := ks.cacheService.SetKeyStatus(cachedStatus, ks.config.CacheTTL); err != nil {
		logger.Error("Failed to update key status cache", "error", err, "key", key)
	}

	return cachedStatus, nil
}

// UpdateKeyStatus 更新Key状态并清除缓存
func (ks *KeyService) UpdateKeyStatus(ctx context.Context, key, status, reason string) error {
	// 更新数据库
	result := cosy.UseDB(ctx).Model(&model.ApiKey{}).
		Where("api_key = ?", key).
		Update("status", status)

	if result.Error != nil {
		return fmt.Errorf("更新Key状态失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("Key不存在: %s", key)
	}

	// 清除缓存
	if err := ks.cacheService.InvalidateKeyStatus(key); err != nil {
		logger.Error("Failed to invalidate key status cache", "error", err, "key", key)
	}

	// 发布状态更新
	ks.mqttService.PublishKeyStatus(key, status, reason)

	logger.Info("Key status updated successfully", "key", key, "status", status, "reason", reason)
	return nil
}

// UpdateKeyStatusBatch 批量更新Key状态并推送消息
func (ks *KeyService) UpdateKeyStatusBatch(updates []KeyStatusUpdate) error {
	if len(updates) == 0 {
		return nil
	}

	// 提取所有需要更新的key
	keys := make([]string, len(updates))
	for i, update := range updates {
		keys[i] = update.Key
	}

	// 批量更新数据库状态
	status := updates[0].Status
	_, err := query.ApiKey.
		Where(query.ApiKey.APIKey.In(keys...)).
		Update(query.ApiKey.Status, status)
	if err != nil {
		return fmt.Errorf("Failed to update key status: %w", err)
	}

	logger.Info("Batch key status updated successfully", "count", len(updates), "status", status)

	// 批量清除缓存
	for _, update := range updates {
		if err := ks.cacheService.InvalidateKeyStatus(update.Key); err != nil {
			logger.Error("Failed to invalidate key status cache", "error", err, "key", update.Key)
		}
	}

	// 批量发布状态更新
	ks.mqttService.PublishKeyStatusBatch(updates)

	return nil
}

// CheckAndRestoreKeyStatus 检查并恢复Key状态（用于充值和添加资源包后）
func (ks *KeyService) CheckAndRestoreKeyStatus(ctx context.Context, userID uint64, apiKey string, module string) error {
	// 查询被阻塞的Key
	q := query.ApiKey.Where(query.ApiKey.UserID.Eq(userID), query.ApiKey.Status.Eq(types.KeyStatusBlocked))

	// 如果指定了apiKey，只处理该Key
	if apiKey != "" {
		q = q.Where(query.ApiKey.APIKey.Eq(apiKey))
	}

	blockedKeys, err := q.Find()
	if err != nil {
		return fmt.Errorf("查询被阻塞的Key失败: %w", err)
	}

	if len(blockedKeys) == 0 {
		return nil // 没有被阻塞的Key
	}

	var keysToRestore []KeyStatusUpdate

	for _, key := range blockedKeys {
		canRestore := false

		// 检查该Key的配额状态
		quotaQuery := query.QuotaPackageRecord.Where(
			query.QuotaPackageRecord.APIKey.Eq(key.APIKey),
			query.QuotaPackageRecord.Status.Eq(types.QuotaPackageStatusActive))

		// 如果指定了模块，只检查该模块
		if module != "" {
			quotaQuery = quotaQuery.Where(query.QuotaPackageRecord.Module.Eq(module))
		}

		quotas, err := quotaQuery.Find()
		if err != nil {
			logger.Error("查询配额失败", "error", err, "key", key.APIKey)
			continue
		}

		// 检查是否有可用的配额
		for _, quota := range quotas {
			if quota.Quota > quota.Used {
				canRestore = true
				break
			}
		}

		// 如果没有足够的配额，检查用户余额
		if !canRestore {
			user, err := query.User.Where(query.User.ID.Eq(userID)).First()
			if err != nil {
				logger.Error("查询用户失败", "error", err, "userID", userID)
				continue
			}

			// 如果用户有余额，也可以恢复
			if user.Balance > 0 {
				canRestore = true
			}
		}

		if canRestore {
			keysToRestore = append(keysToRestore, KeyStatusUpdate{
				Key:    key.APIKey,
				Status: types.KeyStatusOk,
				Reason: "余额充值或配额补充",
			})
		}
	}

	// 批量恢复Key状态
	if len(keysToRestore) > 0 {
		err = ks.UpdateKeyStatusBatch(keysToRestore)
		if err != nil {
			return fmt.Errorf("批量恢复Key状态失败: %w", err)
		}

		logger.Info("成功恢复Key状态", "count", len(keysToRestore), "userID", userID)
	}

	return nil
}
