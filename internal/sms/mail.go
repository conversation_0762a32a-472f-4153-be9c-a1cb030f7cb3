package sms

import (
	"errors"

	"git.uozi.org/uozi/potato-billing-api/settings"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v4/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/uozi-tech/cosy/logger"
)

// 创建阿里云短信客户端
func createClient() (*dysmsapi20170525.Client, error) {
	config := &openapi.Config{
		AccessKeyId:     tea.String(settings.SMSSettings.AccessKeyId),
		AccessKeySecret: tea.String(settings.SMSSettings.AccessKeySecret),
	}
	// API 接入点
	config.Endpoint = tea.String("dysmsapi.aliyuncs.com")
	return dysmsapi20170525.NewClient(config)
}

// Mail 发送短信
func Mail(phone string, templateCode string, templateParam string) error {
	client, err := createClient()
	if err != nil {
		return err
	}

	sendSmsRequest := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  tea.String(phone),
		SignName:      tea.String(settings.SMSSettings.SignName),
		TemplateCode:  tea.String(templateCode),
		TemplateParam: tea.String(templateParam),
	}

	runtime := &util.RuntimeOptions{}
	resp, err := client.SendSmsWithOptions(sendSmsRequest, runtime)
	if err != nil {
		return err
	}

	logger.Debug(resp)
	if *resp.Body.Code != "OK" {
		return errors.New(*resp.Body.Message)
	}
	return nil
}
