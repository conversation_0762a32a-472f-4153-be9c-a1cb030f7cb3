# Sync  "main" branch to "dev" branch

name: Sync branch

on:
  push:
    branches: [ "main" ]
  pull_request:
    types: [ closed ]
    branches: [ "main" ]

jobs:
  sync:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Sync dev with main
        run: |
          # Fetch all branches from origin
          git fetch origin

          # Checkout the dev branch
          git checkout dev

          # Rebase dev onto main
          git rebase origin/main

          # Force push the rebased dev branch
          git push --force-with-lease origin dev
