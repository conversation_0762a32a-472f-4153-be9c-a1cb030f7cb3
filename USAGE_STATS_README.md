# 用量统计模块设计说明

## 问题背景

在计费系统中，不同服务模块的计费单位是不同的：
- **LLM服务**: 按 tokens 计费
- **TTS服务**: 按 characters（字符数）计费
- **ASR服务**: 按 seconds（音频时长秒数）计费

这些不同单位的使用量不能简单相加，因此需要按模块分开统计。

## 解决方案

### 后端实现 (usage_handler.go)

1. **统计逻辑修改**:
   - 按 `module` 分组统计各模块的使用量
   - 添加 `unit` 字段标识每个模块的计量单位
   - 只统计可以相加的指标：总成本、总请求数
   - 不再计算总 tokens（因为单位不同）

2. **返回数据结构**:
   ```go
   type ModuleStatsResult struct {
       Module     string  `json:"module"`
       TotalUsage int64   `json:"total_usage"` // 模块总使用量
       TotalCost  float64 `json:"total_cost"`  // 模块总成本
       Count      int64   `json:"count"`       // 模块请求数
       Unit       string  `json:"unit"`        // 计量单位
   }
   ```

3. **响应结构**:
   - `total_cost`: 所有模块的总成本（可以相加）
   - `total_count`: 所有模块的总请求数（可以相加）
   - `by_module`: 按模块的详细统计

### 前端实现

1. **接口类型定义** (key.ts):
   ```typescript
   export interface UsageStats {
     period: 'day' | 'week' | 'month'
     start_time: number
     end_time: number
     total_cost: number // 总成本（可以相加）
     total_count: number // 总请求数（可以相加）
     by_module: ModuleStats[] // 按模块分开的统计
   }

   export interface ModuleStats {
     module: string
     total_usage: number // 模块使用量
     total_cost: number
     count: number
     unit: string // 计量单位
   }
   ```

2. **显示逻辑** (quota/index.vue):
   - 添加 `formatUsage()` 函数处理不同单位的格式化
   - 统计卡片显示总成本和总请求数，不显示总 tokens
   - 模块统计按单位正确显示使用量

## 单位格式化规则

### tokens 和 characters
- 使用 K/M 格式：1,000 → 1K，1,000,000 → 1M
- 显示格式：`8.2M tokens`, `1.8M characters`

### seconds (音频时长)
- 自动转换为时分秒格式
- 显示格式：
  - `< 60s`: `45s`
  - `60s-3600s`: `15m30s`
  - `> 3600s`: `2h15m30s`

## 统计卡片显示

1. **总资源包**: 显示资源包总数、活跃数、过期数
2. **总成本**: 显示所有模块的总成本（可以相加）
3. **总请求数**: 显示所有模块的总请求次数（可以相加）
4. **即将过期**: 显示7天内即将过期的资源包数量

## 模块统计显示

每个模块单独显示：
- 模块名称和资源包数量
- 使用量/配额（带正确单位）
- 使用率百分比
- 模块总成本

## 注意事项

1. **不要将不同单位的使用量相加**: tokens、characters、seconds 不能直接相加
2. **可以相加的指标**: 成本（都是货币单位）、请求数（都是次数）
3. **单位一致性**: 确保同一模块内的使用量单位一致
4. **显示友好性**: 使用合适的格式化函数提升用户体验

## 相关文件

- `billing-sys-api/api/billing/usage_handler.go`: 后端统计逻辑
- `billing-web/apps/admin/src/api/key.ts`: 前端接口定义
- `billing-web/apps/admin/src/views/billing/quota/index.vue`: 前端显示逻辑
