---
description: API 开发和接口设计规范
---

# API 开发规范

## 总体原则
基于现有计费系统API构建，遵循 RESTful 设计原则，提供统一的接口规范。

## 接口路径设计

### 管理端接口 (/admin)
- `/admin/users` - 用户管理
- `/admin/billing/keys` - API Key 管理
- `/admin/billing/quota_packages` - 资源包管理
- `/admin/billing/pricing_rules` - 定价规则管理
- `/admin/billing/recharge_records` - 充值记录管理
- `/admin/settings/auth` - 认证设置
- `/admin/settings/general` - 通用设置

### 客户端接口 (/frontend)
- `/frontend/user` - 用户相关操作

### 全局接口 (/global)
- `/login` - 用户登录
- `/logout` - 用户退出
- `/upload` - 文件上传
- `/upload_temp` - 临时文件上传

## 请求和响应格式

### 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 实际数据
  }
}
```

### 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": [...],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 100,
    "total_pages": 5
  }
}
```

### 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误",
  "errors": {
    "field": "具体错误信息"
  }
}
```

## 核心业务接口

### 用户管理接口

#### 获取用户列表
```
GET /admin/users
Query: page, page_size, name, email, phone, status
Response: User[]
```

#### 获取用户详情
```
GET /admin/users/:id
Response: User
```

#### 更新用户信息
```
PUT /admin/users/:id
Body: { name?, email?, phone?, status?, avatar_id? }
Response: User
```

#### 重置用户密码
```
POST /admin/users/:id/reset-password
Body: { password }
Response: void
```

#### 生成随机密码
```
POST /admin/users/:id/generate-password
Response: { password }
```

### API Key 管理接口

#### 获取 Key 列表
```
GET /admin/billing/keys
Query: page, page_size, user_id, status, module
Response: ApiKey[]
```

#### 获取 Key 状态详情
```
GET /admin/billing/keys/:key/status
Response: {
  api_key, status, available, quotas[], user, created_at, updated_at
}
```

#### 获取 Key 用量历史
```
GET /admin/billing/keys/:key/usage
Query: page, page_size, module, start_time, end_time
Response: UsageLog[]
```

#### 获取 Key 用量统计
```
GET /admin/billing/keys/:key/stats
Query: period (day|week|month)
Response: {
  period, start_time, end_time,
  total_cost, total_count,
  by_module: [{ module, total_usage, total_cost, count, unit }]
}
```

### 资源包管理接口

#### 创建资源包
```
POST /admin/billing/quota_packages
Body: {
  user_id, api_key?, module, model_name?,
  quota, expires_at?, type?, description?
}
Response: QuotaPackage
```

#### 更新资源包
```
PUT /admin/billing/quota_packages/:id
Body: { quota?, expires_at?, status?, description? }
Response: QuotaPackage
```

#### 禁用/启用资源包
```
POST /admin/billing/quota_packages/:id/disable
POST /admin/billing/quota_packages/:id/enable
Response: void
```

### 充值管理接口

#### 创建充值记录
```
POST /admin/billing/recharge_records
Body: { user_id, amount, type?, description? }
Response: RechargeRecord
```

#### 确认充值
```
POST /admin/billing/recharge_records/:id/confirm
Response: RechargeRecord
```

#### 取消充值
```
POST /admin/billing/recharge_records/:id/cancel
Response: RechargeRecord
```

### 定价规则接口

#### 获取定价规则列表
```
GET /admin/billing/pricing_rules
Query: page, page_size, module, is_active
Response: PricingRule[]
```

#### 创建定价规则
```
POST /admin/billing/pricing_rules
Body: {
  module, model_name?, unit_price, currency,
  unit, priority?, description?
}
Response: PricingRule
```

## 认证和权限

### JWT Token 认证
- Header: `Authorization: Bearer <token>`
- 或 Header: `Token: <token>`
- Token 过期时间：24小时

### 权限控制
- 管理端接口需要管理员权限
- 用户接口需要对应用户权限
- 部分操作需要特殊权限验证

## 数据模型关系

### 核心实体
- **User** - 用户信息（用户名、邮箱、余额等）
- **ApiKey** - API密钥（关联用户、模块类型、状态）
- **QuotaPackageRecord** - 资源包记录（配额、使用量、过期时间）
- **PricingRule** - 定价规则（模块、模型、单价、优先级）
- **UsageLog** - 使用记录（消耗量、费用、计费方式）
- **RechargeRecord** - 充值记录（金额、状态、操作员）

### 关联关系
- User 1:N ApiKey
- User 1:N QuotaPackageRecord
- User 1:N RechargeRecord
- ApiKey 1:N UsageLog
- QuotaPackageRecord 1:N UsageLog

## 开发注意事项

### 缓存策略
- Key状态信息缓存5分钟
- 定价规则缓存1小时
- 配额使用量实时更新缓存

### 并发控制
- 配额扣减使用Redis分布式锁
- 余额变更使用数据库事务

### 性能优化
- 分页查询避免深度分页
- 统计查询适当使用缓存
- 大量数据操作考虑异步处理

### 错误处理
- 统一错误码定义
- 详细错误信息记录日志
- 敏感信息不在响应中暴露
