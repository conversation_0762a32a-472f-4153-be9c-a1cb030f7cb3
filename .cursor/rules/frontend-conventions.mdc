---
globs: *.vue,*.ts,*.js,**/apps/**,**/packages/**
description: Vue.js 前端开发规范和约定
---

# Vue.js 前端开发规范

- use tools `context7` to get project information and `task manager` to manage the task

## 技术栈约定
- Vue 3 + TypeScript + Vite
- 使用 Composition API + `<script setup>`
- UI 组件库：[shadcn-vue](mdc:packages/ui/)
- CRUD 组件：[@billing/curd](mdc:packages/curd/)
- 状态管理：Pinia
- 路由：Vue Router
- 工具库：VueUse
- 包管理：pnpm (monorepo)

## 目录结构规范

### 应用结构 ([apps/admin/](mdc:apps/admin/))
```
src/
├── api/           # API 接口定义
├── components/    # 公共组件
├── layouts/       # 布局组件
├── router/        # 路由配置
├── store/         # Pinia 状态管理
├── views/         # 页面组件
├── types/         # TypeScript 类型定义
└── styles/        # 样式文件
```

### 包结构 ([packages/](mdc:packages/))
- `ui/` - shadcn-vue UI 组件库
- `curd/` - CRUD 操作通用组件

## 代码规范

### Vue 组件规范
```vue
<script setup lang="ts">
// 导入依赖
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 接口定义
interface Props {
  title?: string
}

// 组件属性
const props = withDefaults(defineProps<Props>(), {
  title: '默认标题'
})

// 响应式数据
const loading = ref(false)
const data = ref<any[]>([])

// 计算属性
const filteredData = computed(() => {
  return data.value.filter(item => item.active)
})

// 方法定义
async function fetchData() {
  loading.value = true
  try {
    // API 调用
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<template>
  <div class="container">
    <!-- 模板内容 -->
  </div>
</template>

<style scoped>
/* 样式定义 */
</style>
```

### API 接口规范
- 所有 API 接口定义在 `src/api/` 目录下
- 使用 TypeScript 定义请求和响应类型
- 接口函数命名采用动词+名词形式
- 统一错误处理和请求拦截

### 类型定义规范
- 接口使用 `interface` 关键字
- 类型名称使用 PascalCase
- 必要时使用泛型提高复用性
- 导出常用类型到 `types/` 目录

### 样式规范
- 优先使用 Tailwind CSS 工具类
- 自定义样式使用 `scoped` 避免污染
- 遵循设计系统的颜色和间距规范
- 响应式设计使用移动端优先原则

## 组件使用约定

### UI 组件 (@billing/ui)
```vue
<script setup lang="ts">
import {
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Input,
  Label,
} from '@billing/ui'
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle>标题</CardTitle>
    </CardHeader>
    <CardContent>
      <div class="space-y-4">
        <div>
          <Label for="name">名称</Label>
          <Input id="name" v-model="name" />
        </div>
        <Button @click="handleSubmit">提交</Button>
      </div>
    </CardContent>
  </Card>
</template>
```

### CRUD 组件 (@billing/curd)
```vue
<script setup lang="ts">
import { CurdTable, CurdForm, useCurd } from '@billing/curd'
import { userApi } from '@/api'

// 使用 CRUD 组合函数
const {
  data,
  loading,
  pagination,
  searchQuery,
  handleSearch,
  handleCreate,
  handleUpdate,
  handleDelete,
} = useCurd(userApi)
</script>

<template>
  <div>
    <CurdTable
      :data="data"
      :loading="loading"
      :pagination="pagination"
      @search="handleSearch"
      @create="handleCreate"
      @update="handleUpdate"
      @delete="handleDelete"
    />
  </div>
</template>
```

## 状态管理规范

### Pinia Store 结构
```typescript
// stores/user.ts
export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)

  // 操作方法
  const setUser = (userData: User) => {
    user.value = userData
  }

  const setToken = (tokenValue: string) => {
    token.value = tokenValue
    localStorage.setItem('token', tokenValue)
  }

  const reset = () => {
    user.value = null
    token.value = ''
    localStorage.removeItem('token')
  }

  return {
    user: readonly(user),
    token: readonly(token),
    isLoggedIn,
    setUser,
    setToken,
    reset,
  }
})
```

## 路由配置规范

### 路由元信息
```typescript
// router/index.ts
const routes = [
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/views/users/index.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true,
      showInMenu: true,
      icon: Users,
    },
  },
]
```

## 开发工作流
1. 开发新功能前先查看 [功能设计文档]
2. 优先使用现有的 UI 和 CRUD 组件
3. 新增 API 接口需同时更新类型定义
4. 提交前确保代码通过 ESLint 检查
5. 组件开发遵循单一职责原则
