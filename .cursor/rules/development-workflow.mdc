---
description: 开发工作流程和最佳实践指南
---

# 开发工作流程指南

## 项目启动流程

### 后端开发环境搭建
1. 确保 Go 1.23+ 环境
2. 配置数据库连接 (`app.ini`)
3. 安装依赖：`go mod tidy`
4. 生成数据库查询代码：`./gen.sh`
5. 启动服务：`go run main.go`

### 前端开发环境搭建
1. 确保 Node.js 18+ 和 pnpm
2. 安装依赖：`pnpm install`
3. 启动开发服务器：`pnpm dev`
4. 构建生产版本：`pnpm build`

## 开发规范流程

### 新功能开发流程
1. **需求分析**
   - 查阅 [功能设计文档](mdc:计费系统Web管理端功能设计文档.md)
   - 确认功能模块和接口需求
   - 评估技术实现方案

2. **后端开发**
   - 设计/修改数据模型 (`model/`)
   - 运行 `./gen.sh` 生成查询代码
   - 实现业务逻辑 (`internal/`)
   - 实现 API 接口 (`api/`)
   - 配置路由 (`router/`)

3. **前端开发**
   - 定义 API 接口类型 (`api/`)
   - 实现页面组件 (`views/`)
   - 配置路由 (`router/`)
   - 集成 UI 组件库
   - 使用 CRUD 组件简化开发

4. **测试验证**
   - 单元测试
   - 接口测试
   - 功能测试
   - 端到端测试

### 代码提交规范
```bash
# 提交格式
git commit -m "feat: 添加用户管理功能"
git commit -m "fix: 修复配额计算错误"
git commit -m "docs: 更新API文档"
git commit -m "refactor: 重构计费引擎"
```

## 核心组件使用

### shadcn-vue UI 组件 (@billing/ui)
优先使用项目内置的 UI 组件库：
```vue
<script setup lang="ts">
import {
  Button,
  Card,
  Table,
  Form,
  Dialog,
  Badge,
} from '@billing/ui'
</script>
```

### CRUD 组件 (@billing/curd)
使用封装好的 CRUD 组件快速开发：
```vue
<script setup lang="ts">
import { CurdTable, useCurd } from '@billing/curd'
import { userApi } from '@/api'

const curd = useCurd(userApi)
</script>

<template>
  <CurdTable v-bind="curd" />
</template>
```

### API 接口开发
统一的接口调用方式：
```typescript
// 定义接口类型
export interface User {
  id: string
  name: string
  email: string
}

// 使用 CRUD API
export const userApi = useCurdApi<User>('/admin/users')

// 扩展自定义方法
export const extendedUserApi = extendCurdApi(userApi, {
  resetPassword: async (id: string, password: string) => {
    return await http.post(`/admin/users/${id}/reset-password`, { password })
  }
})
```

## 核心业务流程

### 用户管理流程
1. 用户列表展示 ([apps/admin/src/views/users/index.vue](mdc:apps/admin/src/views/users/index.vue))
2. 用户详情编辑 ([apps/admin/src/views/users/detail.vue](mdc:apps/admin/src/views/users/detail.vue))
3. 用户状态管理
4. 密码重置功能

### API Key 管理流程
1. Key 列表管理 ([apps/admin/src/views/billing/keys/index.vue](mdc:apps/admin/src/views/billing/keys/index.vue))
2. Key 详情展示 ([apps/admin/src/views/billing/keys/detail.vue](mdc:apps/admin/src/views/billing/keys/detail.vue))
3. 配额资源包管理
4. 用量统计分析 ([apps/admin/src/views/billing/keys/stats.vue](mdc:apps/admin/src/views/billing/keys/stats.vue))

### 计费管理流程
1. 定价规则配置 ([apps/admin/src/views/billing/pricing/index.vue](mdc:apps/admin/src/views/billing/pricing/index.vue))
2. 资源包管理 ([apps/admin/src/views/billing/quota/index.vue](mdc:apps/admin/src/views/billing/quota/index.vue))
3. 充值记录管理 ([apps/admin/src/views/billing/recharge/index.vue](mdc:apps/admin/src/views/billing/recharge/index.vue))

## 数据库操作规范

### 模型修改流程
1. 修改 `model/*.go` 文件
2. 运行 `./gen.sh` 重新生成查询代码
3. 更新相关业务逻辑
4. 更新前端类型定义

### 查询操作规范
```go
// 使用生成的查询方法
q := query

// 简单查询
user, err := q.User.FirstByID(userID)

// 条件查询
users, err := q.User.Where(q.User.Status.Eq(1)).Find()

// 关联查询
keys, err := q.ApiKey.Preload(q.ApiKey.User).Find()

// 分页查询
users, count, err := q.User.FindByPage(offset, limit)
```

### 事务处理
```go
// 复杂业务使用事务
err := q.Transaction(func(tx *query.Query) error {
    // 多个数据库操作
    return nil
})
```

## 缓存使用规范

### Redis 缓存策略
- Key状态信息：5分钟缓存
- 定价规则：1小时缓存
- 配额使用量：实时更新
- 用户会话：24小时缓存

### 缓存失效策略
```go
// 数据更新后清理相关缓存
cacheService.InvalidateKeyStatus(apiKey)
cacheService.InvalidateQuotaUsage(keyID, module)
cacheService.InvalidatePricingRules(module, modelName)
```

## 性能优化建议

### 后端优化
1. 使用数据库连接池
2. 合理设计数据库索引
3. 避免 N+1 查询问题
4. 使用 Redis 缓存热点数据
5. 异步处理耗时操作

### 前端优化
1. 使用 Vue 3 的响应式系统
2. 合理使用 computed 和 watch
3. 组件懒加载
4. 图片优化和懒加载
5. 合理使用 Pinia 状态管理
