---
alwaysApply: true
description: 计费系统项目结构和架构指南
---

# 计费系统项目结构指南

## 项目概述
这是一个基于 Go + Vue.js 的计费系统，采用前后端分离架构。

## 目录结构

### 后端 API (billing-sys-api/)
- **[api/](mdc:api/)** - HTTP 接口处理器，按功能模块组织
  - `admin/` - 管理端接口 (用户管理、设置)
  - `billing/` - 计费相关接口 (API Key、配额、定价)
  - `frontend/` - 客户端接口
  - `global/` - 全局接口 (认证、上传、用户)

- **[internal/](mdc:internal/)** - 内部业务逻辑
  - `billing/` - 计费核心服务 (引擎、缓存、MQTT、定时任务)
  - `user/` - 用户相关服务
  - `helper/` - 工具函数
  - `middleware/` - HTTP 中间件

- **[model/](mdc:model/)** - 数据模型定义 (GORM)
- **[query/](mdc:query/)** - 自动生成的查询代码 (gen)
- **[router/](mdc:router/)** - 路由配置
- **[settings/](mdc:settings/)** - 配置管理

### 前端 Web (billing-web/)
- **[apps/admin/](mdc:apps/admin/)** - 管理端应用
- **[packages/ui/](mdc:packages/ui/)** - shadcn-vue UI 组件库
- **[packages/curd/](mdc:packages/curd/)** - CRUD 操作组件库

## 技术栈
- **后端**: Go + Gin + GORM + Redis + MQTT
- **前端**: Vue 3 + TypeScript + Vite + shadcn-vue + Pinia
- **构建**: monorepo + pnpm

## 关键文件
- **[main.go](mdc:main.go)** - 后端服务入口
- **[gen.sh](mdc:gen.sh)** - 数据库代码生成脚本
