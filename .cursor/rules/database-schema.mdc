---
globs: **/model/**,**/query/**,gen.sh
description: 数据库模型和架构设计规范
---

# 数据库模型设计规范

## 核心数据模型

### 基础模型 ([model/model.go](mdc:model/model.go))
所有实体都继承基础 `Model` 结构体：
```go
type Model struct {
    ID        uint64                `gorm:"primary_key" json:"id,string"`
    CreatedAt int64                 `json:"created_at,omitempty" gorm:"autoCreateTime"`
    UpdatedAt int64                 `json:"updated_at,omitempty" gorm:"autoUpdateTime"`
    DeletedAt soft_delete.DeletedAt `json:"deleted_at,omitempty" gorm:"index;default:0"`
}
```

## 核心实体模型

### 用户模型 ([model/user.go](mdc:model/user.go))
```go
type User struct {
    Model
    Name       string  `json:"name" cosy:"add:required;list:fussy"`
    Password   string  `json:"-" cosy:"add:required"`
    Email      string  `json:"email" gorm:"index" cosy:"add:required,email;list:fussy"`
    Phone      string  `json:"phone" gorm:"index" cosy:"list:fussy"`
    Avatar     *Upload `json:"avatar" gorm:"foreignKey:avatar_id"`
    Balance    float64 `json:"balance" gorm:"type:decimal(10,4);default:0"`
    LastActive int64   `json:"last_active"`
    Status     int     `json:"status" gorm:"default:1"`
}
```

### API Key 模型 ([model/api_key.go](mdc:model/api_key.go))
```go
type ApiKey struct {
    Model
    APIKey    string                `json:"api_key" gorm:"uniqueIndex"`
    Name      string                `json:"name" gorm:"type:varchar(255)"`
    Module    string                `json:"module" gorm:"default:'llm'"`
    Status    string                `json:"status" gorm:"default:'ok'"`
    UserID    uint64                `json:"user_id,string" gorm:"index"`
    User      *User                 `json:"user" gorm:"foreignKey:UserID"`
    Comment   string                `json:"comment" gorm:"type:text"`
}
```

### 资源包记录模型 ([model/quota_package_record.go](mdc:model/quota_package_record.go))
```go
type QuotaPackageRecord struct {
    Model
    UserID      uint64 `json:"user_id,string" gorm:"index"`
    User        *User  `json:"user" gorm:"foreignKey:UserID"`
    APIKey      string `json:"api_key" gorm:"index"`
    Module      string `json:"module" gorm:"not null"`
    ModelName   string `json:"model_name"`
    Quota       int64  `json:"quota" gorm:"not null"`
    Used        int64  `json:"used" gorm:"default:0"`
    Available   int64  `json:"available" gorm:"-"`
    ExpiresAt   int64  `json:"expires_at" gorm:"index"`
    Status      string `json:"status" gorm:"default:'active'"`
    Type        string `json:"type" gorm:"default:'admin'"`
    Description string `json:"description" gorm:"type:text"`
    OperatorID  uint64 `json:"operator_id,string" gorm:"index"`
    Operator    *User  `json:"operator" gorm:"foreignKey:OperatorID"`
}
```

### 定价规则模型 ([model/pricing_rule.go](mdc:model/pricing_rule.go))
```go
type PricingRule struct {
    Model
    Module      string  `json:"module" gorm:"not null"`
    ModelName   string  `json:"model_name"`
    UnitPrice   float64 `json:"unit_price" gorm:"type:decimal(10,4)"`
    Currency    string  `json:"currency" gorm:"default:'CNY'"`
    Unit        string  `json:"unit" gorm:"default:'token'"`
    IsActive    bool    `json:"is_active" gorm:"default:true"`
    Priority    int     `json:"priority" gorm:"default:0"`
    Description string  `json:"description" gorm:"type:text"`
}
```

### 使用记录模型 ([model/usage_log.go](mdc:model/usage_log.go))
```go
type UsageLog struct {
    Model
    APIKey         string  `json:"api_key" gorm:"index"`
    Module         string  `json:"module" gorm:"not null"`
    ModelName      string  `json:"model_name"`
    Usage          int64   `json:"usage" gorm:"not null"`
    UnitPrice      float64 `json:"unit_price" gorm:"type:decimal(10,4)"`
    Cost           float64 `json:"cost" gorm:"type:decimal(10,4)"`
    Currency       string  `json:"currency" gorm:"default:'CNY'"`
    Unit           string  `json:"unit" gorm:"default:'token'"`
    BillingType    string  `json:"billing_type" gorm:"default:'quota'"`
    QuotaPackageID uint64  `json:"quota_package_id,string" gorm:"index"`
    UserID         uint64  `json:"user_id,string" gorm:"index"`
}
```

### 充值记录模型 ([model/recharge_record.go](mdc:model/recharge_record.go))
```go
type RechargeRecord struct {
    Model
    UserID      uint64  `json:"user_id,string" gorm:"index"`
    User        *User   `json:"user" gorm:"foreignKey:UserID"`
    Amount      float64 `json:"amount" gorm:"type:decimal(10,4)"`
    Type        string  `json:"type" gorm:"default:'admin'"`
    Status      string  `json:"status" gorm:"default:'completed'"`
    TradeNo     string  `json:"trade_no"`
    Description string  `json:"description" gorm:"type:text"`
    OperatorID  uint64  `json:"operator_id,string" gorm:"index"`
    Operator    *User   `json:"operator" gorm:"foreignKey:OperatorID"`
}
```

## 数据库设计原则

### 字段命名规范
- 使用 snake_case 命名法
- 主键统一命名为 `id`
- 外键命名为 `关联表名_id`
- 时间字段使用 Unix 时间戳 (int64)
- 金额字段使用 decimal(10,4) 类型

### 索引设计
- 主键自动创建聚簇索引
- 外键字段创建普通索引
- 查询频繁的字段创建组合索引
- 唯一约束字段创建唯一索引

### 关联关系
- 用户与其他实体为一对多关系
- 使用 GORM 外键约束管理关联
- 预加载关联数据使用 `cosy:"list:preload"`
- 软删除使用 `soft_delete.DeletedAt`

## 数据生成和查询

### 代码生成 ([gen.sh](mdc:gen.sh))
修改数据库模型后，必须运行代码生成：
```bash
./gen.sh
```

生成的查询代码位于 [query/](mdc:query/) 目录：
- `api_keys.gen.go` - API Key 查询方法
- `users.gen.go` - 用户查询方法
- `quota_package_records.gen.go` - 资源包查询方法
- `pricing_rules.gen.go` - 定价规则查询方法
- `usage_logs.gen.go` - 使用记录查询方法
- `recharge_records.gen.go` - 充值记录查询方法

### 查询方法使用
```go
// 基础查询
q := query

// 按ID查询
user, err := q.User.FirstByID(userID)

// 条件查询
keys, err := q.ApiKey.Where(q.ApiKey.UserID.Eq(userID)).Find()

// 预加载关联
keys, err := q.ApiKey.Preload(q.ApiKey.User).Find()

// 分页查询
result, count, err := q.User.
    FindByPage(offset, limit)
```

## 计费业务逻辑

### 计费类型
1. **配额计费 (quota)** - 优先使用资源包
2. **余额计费 (balance)** - 扣除用户余额
3. **混合计费** - 先配额再余额

### 配额管理
- 支持按模块 (llm/tts/asr) 独立配额
- 支持按模型 (model_name) 独立配额
- 配额状态：active(生效)、expired(过期)、exhausted(用完)、disabled(禁用)

### 资源包类型
- `admin` - 管理员赠送
- `purchase` - 用户购买
- `promotion` - 促销活动

### 充值类型
- `admin` - 管理员充值
- `alipay` - 支付宝
- `wechat` - 微信
- `bank` - 银行转账

## 数据一致性

### 事务处理
- 计费操作使用数据库事务
- 配额扣减和余额变更保证原子性
- 并发操作使用 Redis 分布式锁

### 缓存同步
- 配额变更后清理相关缓存
- Key状态变更后更新缓存
- 定价规则变更后清理缓存

### 数据完整性
- 外键约束保证关联数据完整性
- 唯一约束防止重复数据
- 检查约束保证数据有效性
