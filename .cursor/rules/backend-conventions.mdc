---
globs: *.go,**/api/**,**/internal/**,**/model/**,**/query/**,**/router/**,**/settings/**
description: Go 后端开发规范和约定
---

# Go 后端开发规范

## 代码规范
- 使用 Go 1.23 或更新版本
- 遵循标准 Go 代码风格，使用 `gofmt` 格式化
- 函数名采用驼峰命名，导出函数首字母大写
- 包名使用小写，避免下划线
- 常量使用大写字母加下划线

## 项目约定

### API 处理器结构
```go
// 在 api/ 目录下，按模块组织
func HandlerName(c *gin.Context) {
    // 1. 参数绑定和验证
    var req RequestStruct
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }

    // 2. 业务逻辑调用
    result, err := service.DoSomething(req)
    if err != nil {
        c.<PERSON>(500, gin.H{"error": err.<PERSON><PERSON>r()})
        return
    }

    // 3. 返回结果
    c.<PERSON>(200, result)
}
```

### 数据模型定义 ([model/](mdc:model/))
- 继承 `Model` 基础结构体，包含 ID、CreatedAt、UpdatedAt、DeletedAt
- 使用 GORM 标签定义数据库字段
- 使用 `cosy` 标签定义 API 参数验证
- JSON 标签使用 snake_case
- 关联关系使用指针类型

### 服务层结构 ([internal/](mdc:internal/))
- 业务逻辑封装在 `internal/` 目录下
- 服务使用单例模式，提供 `GetXXXService()` 函数
- 核心服务包括：
  - `BillingService` - 计费主服务
  - `CacheService` - Redis 缓存服务
  - `KeyService` - API Key 管理
  - `PricingService` - 定价规则服务
  - `QuotaService` - 配额管理服务

### 错误处理
- 使用 `error` 接口返回错误
- 数据库操作失败时返回具体错误信息
- API 层统一处理错误格式

### 数据库操作
- 使用 GORM 进行数据库操作
- 查询代码通过 [gen.sh](mdc:gen.sh) 自动生成到 [query/](mdc:query/) 目录
- 修改数据库模型后必须运行 `./gen.sh` 重新生成代码
- 使用事务处理复杂业务逻辑

### 缓存策略
- 使用 Redis 缓存热点数据
- Key 状态信息缓存 5 分钟
- 定价规则缓存 1 小时
- 配额使用量实时缓存

### 配置管理
- 配置文件定义在 [settings/](mdc:settings/) 目录
- 使用 `app.ini` 文件进行配置
- 支持环境变量覆盖配置项
