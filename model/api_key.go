package model

import (
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

// ApiKey Key管理表
type ApiKey struct {
	Model
	APIKey  string `json:"api_key" gorm:"uniqueIndex;not null;type:varchar(255)"`                                                                  // API Key
	Name    string `json:"name" gorm:"type:varchar(255)" cosy:"add:required;update:omitempty;list:fussy"`                                          // Key名称
	Status  string `json:"status" cosy:"all:omitempty" gorm:"not null;default:'ok';type:varchar(20)" cosy:"add:required;update:omitempty;list:in"` // 'ok' | 'blocked'
	UserID  uint64 `json:"user_id,string" gorm:"index" cosy:"add:required;list:fussy"`                                                             // 关联用户ID
	User    *User  `json:"user,omitempty" gorm:"foreignKey:UserID" cosy:"list:preload"`                                                            // 关联用户
	Comment string `json:"comment" gorm:"type:text" cosy:"all:omitempty"`
}

// BeforeCreate 创建前生成ID
func (ak *ApiKey) BeforeCreate(_ *gorm.DB) error {
	if ak.ID == 0 {
		ak.ID = sonyflake.NextID()
	}
	return nil
}
