package model

import (
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

// RechargeRecord 用户充值记录表
type RechargeRecord struct {
	Model
	UserID      uint64                 `json:"user_id,string" gorm:"not null;index"`                                                     // 用户ID
	User        *User                  `json:"user,omitempty" gorm:"foreignKey:UserID" cosy:"list:preload"`                              // 关联用户
	Amount      float64                `json:"amount" gorm:"type:decimal(10,4);not null" cosy:"add:required,min=0"`                      // 充值金额
	Type        string                 `json:"type" gorm:"not null;type:varchar(20);default:'admin'" cosy:"add:omitempty;list:in"`       // 充值类型: admin(管理员充值), alipay(支付宝), wechat(微信), bank(银行转账), other(其他)
	Status      string                 `json:"status" gorm:"not null;type:varchar(20);default:'completed'" cosy:"add:omitempty;list:in"` // 状态: pending(待处理), completed(已完成), failed(失败), cancelled(已取消)
	TradeNo     string                 `json:"trade_no" gorm:"type:varchar(100)" cosy:"all:omitempty"`                                   // 交易号
	Description string                 `json:"description" gorm:"type:text" cosy:"all:omitempty"`                                        // 充值描述
	Metadata    map[string]interface{} `json:"metadata,omitempty" gorm:"serializer:json"`                                                // 附加信息
	OperatorID  uint64                 `json:"operator_id,string,omitempty" gorm:"index"`                                                // 操作员ID（管理员充值时使用）
	Operator    *User                  `json:"operator,omitempty" gorm:"foreignKey:OperatorID"`                                          // 操作员信息
}

// BeforeCreate 创建前生成ID
func (rr *RechargeRecord) BeforeCreate(_ *gorm.DB) error {
	if rr.ID == 0 {
		rr.ID = sonyflake.NextID()
	}
	return nil
}
