package model

import (
	"context"
	"testing"

	"git.uozi.org/uozi/potato-billing-api/internal/helper"
	"github.com/spf13/cast"
	"github.com/stretchr/testify/assert"
	"github.com/uozi-tech/cosy"
	cModel "github.com/uozi-tech/cosy/model"
	"github.com/uozi-tech/cosy/redis"
	"github.com/uozi-tech/cosy/sandbox"
)

func TestModelUserGetPermissionsMap(t *testing.T) {
	sandbox.NewInstance("../app.testing.ini", "mysql").
		RegisterModels(User{}).
		Run(func(instance *sandbox.Instance) {
			Use(cModel.UseDB(context.Background()))
			// create user
			user := &User{
				Name:     "test",
				Password: "test",
				Email:    "test",
				Phone:    "test",
			}
			err := db.Create(user).Error
			if err != nil {
				t.Error(err)
			}

			value, _ := redis.Get(helper.BuildUserKey(user.ID, "last_active"))
			assert.Equal(t, "", value)
		})
}

func TestUpdateLastActive(t *testing.T) {
	sandbox.NewInstance("../app.testing.ini", "mysql").
		RegisterModels(User{}).
		Run(func(instance *sandbox.Instance) {
			Use(cosy.UseDB(context.Background()))
			user := &User{
				Model: Model{
					ID: 1,
				},
			}

			now := user.UpdateLastActive()

			key := helper.BuildUserKey(1, "last_active")

			value, err := redis.Get(key)
			if err != nil {
				t.Error(err)
			}

			assert.Equal(t, now, cast.ToInt64(value))
		})
}
