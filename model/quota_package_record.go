package model

import (
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

// QuotaPackageRecord 资源包记录表
type QuotaPackageRecord struct {
	Model
	UserID      uint64                 `json:"user_id,string" gorm:"not null;index"`                                                  // 用户ID
	User        *User                  `json:"user,omitempty" gorm:"foreignKey:UserID" cosy:"list:preload"`                           // 关联用户
	APIKey      string                 `json:"api_key" gorm:"type:varchar(255);index" cosy:"all:omitempty"`                           // 关联API Key（可选，如果指定则只对该Key生效）
	Module      string                 `json:"module" gorm:"not null;type:varchar(50)" cosy:"add:required;list:in"`                   // 模块: 'llm' | 'tts' | 'asr'
	ModelName   string                 `json:"model_name" gorm:"type:varchar(100)" cosy:"list:fussy"`                                 // 模型名称，可选
	Quota       int64                  `json:"quota" gorm:"not null" cosy:"add:required,min=1"`                                       // 资源包配额
	Used        int64                  `json:"used" gorm:"not null;default:0"`                                                        // 已用量
	Available   int64                  `json:"available" gorm:"-"`                                                                    // 可用量(计算字段)
	ExpiresAt   int64                  `json:"expires_at,omitempty" gorm:"index"`                                                     // 过期时间
	Status      string                 `json:"status" gorm:"not null;type:varchar(20);default:'active'" cosy:"add:omitempty;list:in"` // 状态: active(生效), expired(过期), exhausted(用完), disabled(禁用)
	Type        string                 `json:"type" gorm:"not null;type:varchar(20);default:'admin'" cosy:"add:omitempty;list:in"`    // 类型: admin(管理员赠送), purchase(购买), promotion(促销活动)
	Description string                 `json:"description" gorm:"type:text" cosy:"all:omitempty"`                                     // 资源包描述
	Metadata    map[string]interface{} `json:"metadata,omitempty" gorm:"serializer:json"`                                             // 附加信息
	OperatorID  uint64                 `json:"operator_id,string,omitempty" gorm:"index"`                                             // 操作员ID（管理员添加时使用）
	Operator    *User                  `json:"operator,omitempty" gorm:"foreignKey:OperatorID"`                                       // 操作员信息
}

// BeforeCreate 创建前生成ID
func (qpr *QuotaPackageRecord) BeforeCreate(_ *gorm.DB) error {
	if qpr.ID == 0 {
		qpr.ID = sonyflake.NextID()
	}
	return nil
}

// AfterFind 计算可用量
func (qpr *QuotaPackageRecord) AfterFind(_ *gorm.DB) error {
	qpr.Available = qpr.Quota - qpr.Used
	if qpr.Available < 0 {
		qpr.Available = 0
	}
	return nil
}
