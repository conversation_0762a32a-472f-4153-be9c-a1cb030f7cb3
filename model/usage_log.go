package model

import (
	"github.com/uozi-tech/cosy/sonyflake"
	"gorm.io/gorm"
)

// UsageLog 用量日志表
type UsageLog struct {
	Model
	APIKey         string                 `json:"api_key" gorm:"not null;index;type:varchar(255)"`        // Key值(冗余字段，方便查询)
	Module         string                 `json:"module" gorm:"not null;type:varchar(50)" cosy:"list:in"` // 'llm' | 'tts' | 'asr'
	ModelName      string                 `json:"model_name" gorm:"type:varchar(100)" cosy:"list:fussy"`  // 模型名称，可选
	Usage          int64                  `json:"usage" gorm:"not null"`                                  // 用量
	UnitPrice      float64                `json:"unit_price" gorm:"type:decimal(10,4);default:0"`         // 单价
	Cost           float64                `json:"cost" gorm:"type:decimal(10,4);default:0"`               // 费用
	Currency       string                 `json:"currency" gorm:"type:varchar(10);default:'CNY'"`         // 货币类型
	Unit           string                 `json:"unit" gorm:"type:varchar(20);default:'token'"`           // 计费单位
	PricingRuleID  uint64                 `json:"pricing_rule_id,string,omitempty" gorm:"index"`          // 冗余字段，当时生效的价格规则ID
	BillingType    string                 `json:"billing_type" gorm:"type:varchar(20);default:'quota'"`   // 计费类型: quota(资源包), balance(余额)
	QuotaPackageID uint64                 `json:"quota_package_id,string,omitempty" gorm:"index"`         // 冗余字段，使用的资源包ID（如果是资源包计费）
	UserID         uint64                 `json:"user_id,string,omitempty" gorm:"index"`                  // 用户ID
	Metadata       map[string]interface{} `json:"metadata,omitempty" gorm:"serializer:json"`
}

// BeforeCreate 创建前生成ID
func (ul *UsageLog) BeforeCreate(_ *gorm.DB) error {
	if ul.ID == 0 {
		ul.ID = sonyflake.NextID()
	}
	return nil
}
