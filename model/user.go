package model

import (
	"errors"
	"time"

	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"

	"git.uozi.org/uozi/potato-billing-api/internal/helper"
	"github.com/uozi-tech/cosy/redis"
)

const (
	UserStatusActive = 1
	UserStatusBan    = -1
)

type User struct {
	Model
	Name       string  `json:"name,omitempty" cosy:"add:required;update:omitempty;list:fussy"`
	Password   string  `json:"-" cosy:"json:password;add:required;update:omitempty"` // hide password
	Email      string  `json:"email,omitempty" cosy:"add:required,email;update:omitempty,email;list:fussy" gorm:"type:varchar(255);index"`
	PhoneLike  []byte  `json:"-" gorm:"type:varbinary(5120);serializer:crypto[sakura]"`
	Phone      string  `json:"phone" cosy:"all:omitempty;list:fussy[sakura]" gorm:"serializer:crypto;type:varchar(32)" gorm:"index"`
	AvatarID   uint64  `json:"avatar_id,string,omitempty" cosy:"all:omitempty"`
	Avatar     *Upload `json:"avatar,omitempty" gorm:"foreignKey:avatar_id"`
	Balance    float64 `json:"balance" gorm:"type:decimal(10,6);default:0" cosy:"all:omitempty"` // 用户余额
	LastActive int64   `json:"last_active,omitempty"`
	Status     int     `json:"status,omitempty" cosy:"add:omitempty,min=-1,max=1;update:omitempty,min=-1,max=1;list:in" gorm:"default:1"`
}

func (u *User) AfterUpdate(_ *gorm.DB) (err error) {
	if u.ID == 0 {
		logger.Warn("the after update hook of user model detected an invalid user id(0), " +
			"this will not clean the cache of the user you expected")
		return
	}
	key := helper.BuildUserKey(u.ID)
	err = redis.Del(key)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return
}

// UpdateLastActive update last active time in redis
func (u *User) UpdateLastActive() (now int64) {
	now = time.Now().Unix()
	key := helper.BuildUserKey(u.ID, "last_active")
	_ = redis.Set(key, now, 0)
	return
}

// Updates User
func (u *User) Updates(data *User) error {
	return db.Model(u).Updates(data).Error
}

// AddBalance 增加用户余额
func (u *User) AddBalance(amount float64) error {
	return db.Model(u).Update("balance", gorm.Expr("balance + ?", amount)).Error
}

// SubtractBalance 扣减用户余额
func (u *User) SubtractBalance(amount float64) error {
	if !u.HasSufficientBalance(amount) {
		return errors.New("余额不足")
	}
	return db.Model(u).Update("balance", gorm.Expr("balance - ?", amount)).Error
}

// HasSufficientBalance 检查余额是否充足
func (u *User) HasSufficientBalance(amount float64) bool {
	return u.Balance >= amount
}
