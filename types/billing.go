package types

const (
	// ModuleLLM 大模型
	ModuleLLM = "llm"
	// ModuleTTS 文本转语音
	ModuleTTS = "tts"
	// ModuleASR 语音转文本
	ModuleASR = "asr"
)

const (
	// KeyStatusOk 正常
	KeyStatusOk = "ok"
	// KeyStatusBlocked 不可用
	KeyStatusBlocked = "blocked"
)

const (
	// QuotaPackageStatusActive 正常
	QuotaPackageStatusActive = "active"
	// QuotaPackageStatusExpired 过期
	QuotaPackageStatusExpired = "expired"
	// QuotaPackageStatusExhausted 耗尽
	QuotaPackageStatusExhausted = "exhausted"
	// QuotaPackageStatusDisabled 禁用
	QuotaPackageStatusDisabled = "disabled"
)

const (
	// QuotaPackageSourceAdmin 管理员赠送
	QuotaPackageSourceAdmin = "admin"
	// QuotaPackageSourcePurchase 购买
	QuotaPackageSourcePurchase = "purchase"
	// QuotaPackageSourcePromotion 促销活动
	QuotaPackageSourcePromotion = "promotion"
)

const (
	// PricingUnitToken 按token计费
	PricingUnitToken = "token"
	// PricingUnitCharacter 按字符计费
	PricingUnitCharacter = "character"
	// PricingUnitSecond 按秒计费
	PricingUnitSecond = "second"
)

const (
	// PricingCurrencyCNY 人民币
	PricingCurrencyCNY = "CNY"
	// PricingCurrencyUSD 美元
	PricingCurrencyUSD = "USD"
)

const (
	// RechargeTypeAdmin 管理员充值
	RechargeTypeAdmin = "admin"
	// RechargeTypeAlipay 支付宝
	RechargeTypeAlipay = "alipay"
	// RechargeTypeWechat 微信
	RechargeTypeWechat = "wechat"
	// RechargeTypeBank 银行转账
	RechargeTypeBank = "bank"
	// RechargeTypeOther 其他
	RechargeTypeOther = "other"
)

const (
	// RechargeStatusPending 待处理
	RechargeStatusPending = "pending"
	// RechargeStatusCompleted 已完成
	RechargeStatusCompleted = "completed"
	// RechargeStatusFailed 失败
	RechargeStatusFailed = "failed"
	// RechargeStatusCancelled 已取消
	RechargeStatusCancelled = "cancelled"
)

const (
	// BillingTypeQuota 资源包计费
	BillingTypeQuota = "quota"
	// BillingTypeBalance 余额计费
	BillingTypeBalance = "balance"
)

// BillingRequest 计费请求
type BillingRequest struct {
	APIKey    string                 `json:"api_key"`
	Module    string                 `json:"module"`
	ModelName string                 `json:"model_name"`
	Usage     int64                  `json:"usage"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// BillingResult 计费结果
type BillingResult struct {
	Success        bool    `json:"success"`
	Cost           float64 `json:"cost"`
	BillingType    string  `json:"billing_type"`     // quota(资源包) 或 balance(余额)
	QuotaPackageID uint64  `json:"quota_package_id"` // 使用的资源包ID
	PricingRuleID  uint64  `json:"pricing_rule_id"`  // 使用的价格规则ID
	Message        string  `json:"message"`
}

// QuotaBillingResult 资源包计费结果
type QuotaBillingResult struct {
	Success        bool   `json:"success"`
	QuotaPackageID uint64 `json:"quota_package_id"`
	Message        string `json:"message"`
}
