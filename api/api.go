package api

import (
	"net/http"
	"net/url"
	"time"

	internalUser "git.uozi.org/uozi/potato-billing-api/internal/user"
	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/uozi-tech/cosy/redis"
)

func CurrentUser(c *gin.Context) *model.User {
	return c.MustGet("user").(*model.User)
}

func BuildTokenKey(token string) string {
	return "token:" + token
}

func BuildUserKey(userID uint64) string {
	return "user:" + cast.ToString(userID)
}

func GenerateToken(user *model.User) (string, error) {
	token, err := internalUser.GenerateJWT(user.ID)
	if err != nil {
		return "", err
	}

	expiration := time.Hour * 6

	err = redis.Set(BuildTokenKey(token), user.ID, expiration)
	return token, err
}

func FileAttachmentFromBytes(c *gin.Context, data []byte, filename string) {
	c.<PERSON>.Header().Set("Content-Disposition", `attachment; filename*=UTF-8''`+url.QueryEscape(filename))
	c.Data(http.StatusOK, "application/octet-stream", data)
}
