package statistics

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"

	"github.com/uozi-tech/cosy/logger"
)

// DashboardStatsResponse Dashboard统计响应结构
type DashboardStatsResponse struct {
	// 核心指标
	TotalRevenue float64 `json:"total_revenue"` // 总收入
	MonthRevenue float64 `json:"month_revenue"` // 本月收入
	TodayRevenue float64 `json:"today_revenue"` // 今日收入
	TotalUsers   int64   `json:"total_users"`   // 总用户数
	ActiveUsers  int64   `json:"active_users"`  // 活跃用户数
	TotalKeys    int64   `json:"total_keys"`    // 总Key数
	ActiveKeys   int64   `json:"active_keys"`   // 活跃Key数
	BlockedKeys  int64   `json:"blocked_keys"`  // 阻止Key数

	// 计费相关
	TotalQuotaPackages  int64   `json:"total_quota_packages"`  // 总资源包数
	ActiveQuotaPackages int64   `json:"active_quota_packages"` // 活跃资源包数
	TotalBalance        float64 `json:"total_balance"`         // 用户总余额
	AvgUserBalance      float64 `json:"avg_user_balance"`      // 平均用户余额

	// 使用量统计
	TotalUsage    int64 `json:"total_usage"`     // 总使用量
	MonthUsage    int64 `json:"month_usage"`     // 本月使用量
	TodayUsage    int64 `json:"today_usage"`     // 今日使用量
	AvgDailyUsage int64 `json:"avg_daily_usage"` // 平均日使用量

	// 趋势数据
	RevenueGrowth float64 `json:"revenue_growth"` // 收入增长率
	UserGrowth    float64 `json:"user_growth"`    // 用户增长率
	UsageGrowth   float64 `json:"usage_growth"`   // 使用量增长率
	BalanceGrowth float64 `json:"balance_growth"` // 余额增长率

	// 模块统计
	ModuleStats []ModuleStatsItem `json:"module_stats"`
}

// ModuleStatsItem 模块统计项
type ModuleStatsItem struct {
	Module  string  `json:"module"`  // 模块名称
	Name    string  `json:"name"`    // 显示名称
	Usage   int64   `json:"usage"`   // 使用量
	Revenue float64 `json:"revenue"` // 收入
	Keys    int64   `json:"keys"`    // Key数量
	Growth  float64 `json:"growth"`  // 增长率
	Color   string  `json:"color"`   // 颜色
}

// RecentActiveUser 近期活跃用户
type RecentActiveUser struct {
	ID         string  `json:"id"`
	Name       string  `json:"name"`
	Email      string  `json:"email"`
	Balance    float64 `json:"balance"`
	Usage      int64   `json:"usage"`
	LastActive string  `json:"last_active"`
}

// SystemHealthStatus 系统健康状态
type SystemHealthStatus struct {
	APIResponseTime   int     `json:"api_response_time"`  // API响应时间(ms)
	ErrorRate         float64 `json:"error_rate"`         // 错误率
	Uptime            float64 `json:"uptime"`             // 系统可用性
	QueueDepth        int     `json:"queue_depth"`        // 队列深度
	ActiveConnections int64   `json:"active_connections"` // 活跃连接数
	LastCheck         string  `json:"last_check"`         // 最后检查时间
}

// RecentRecharge 近期充值记录
type RecentRecharge struct {
	ID     string  `json:"id"`
	User   string  `json:"user"`
	Amount float64 `json:"amount"`
	Type   string  `json:"type"`
	Time   string  `json:"time"`
}

// DashboardDataResponse Dashboard完整数据响应
type DashboardDataResponse struct {
	Stats             DashboardStatsResponse `json:"stats"`
	RecentActiveUsers []RecentActiveUser     `json:"recent_active_users"`
	SystemHealth      SystemHealthStatus     `json:"system_health"`
	RecentRecharges   []RecentRecharge       `json:"recent_recharges"`
}

// GetDashboardStats 获取Dashboard统计数据
func GetDashboardStats(c *gin.Context) {
	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 调用统计服务获取数据
	stats, err := statService.GetDashboardStats(c.Request.Context())
	if err != nil {
		logger.Error("获取Dashboard统计数据失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计数据失败"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetDashboardData 获取Dashboard完整数据
func GetDashboardData(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 使用封装的stat service获取所有统计数据
	stats, err := statService.GetDashboardStats(ctx)
	if err != nil {
		logger.Error("获取仪表板统计数据失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计数据失败"})
		return
	}

	// 获取近期活跃用户
	recentActiveUsers, err := statService.GetRecentActiveUsers(ctx, 5)
	if err != nil {
		logger.Error("获取近期活跃用户失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取近期活跃用户失败"})
		return
	}

	// 获取系统健康状态
	// systemHealth := statService.GetSystemHealth(ctx)
	// if err != nil {
	// 	logger.Error("获取系统健康状态失败", "error", err)
	// 	c.JSON(http.StatusInternalServerError, gin.H{"error": "获取系统健康状态失败"})
	// 	return
	// }

	// 获取近期充值记录
	recentRecharges, err := statService.GetRecentRecharges(ctx, 5)
	if err != nil {
		logger.Error("获取近期充值记录失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取近期充值记录失败"})
		return
	}

	// 构建响应数据
	response := gin.H{
		"stats":               stats,
		"recent_active_users": recentActiveUsers,
		// "system_health":    systemHealth,
		"recent_recharges": recentRecharges,
	}

	c.JSON(http.StatusOK, response)
}

// InitDashboardRouter 初始化Dashboard路由
func InitDashboardRouter(g *gin.RouterGroup) {
	g.GET("/dashboard/stats", GetDashboardStats)
	g.GET("/dashboard/data", GetDashboardData)
}
