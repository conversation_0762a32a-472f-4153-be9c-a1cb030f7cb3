package user

import (
	"context"
	"fmt"
	"net/http"
	"path/filepath"
	"time"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/oss"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"github.com/uozi-tech/cosy/map2struct"
	"github.com/uozi-tech/cosy/redis"
	"gorm.io/gorm"
)

func GetCurrentUser(c *gin.Context) {
	user := api.CurrentUser(c)
	if user.AvatarID != 0 {
		func() {
			u := query.Upload
			avatar, err := u.Where(u.ID.Eq(user.AvatarID)).First()
			if err != nil {
				logger.Error(err)
				return
			}
			user.Avatar = avatar
		}()
	}
	c.<PERSON>(http.StatusOK, user)
}

func UpdateCurrentUser(c *gin.Context) {
	cosy.Core[model.User](c).
		SetValidRules(gin.H{
			"avatar": "omitempty",
			"name":   "omitempty",
			"phone":  "omitempty",
			"email":  "omitempty,email",
			"memory": "omitempty",
		}).
		Custom(func(ctx *cosy.Ctx[model.User]) {
			var upload *model.Upload

			o, err := oss.NewOSS()
			if err != nil {
				cosy.ErrHandler(c, err)
				return
			}

			if avatar := ctx.Payload["avatar"]; avatar != nil {
				uploadRes := oss.UploadResult{}
				err := map2struct.WeakDecode(avatar, &uploadRes)
				if err != nil {
					cosy.ErrHandler(c, err)
					return
				}
				dest := fmt.Sprintf("avatar/%v", uuid.New().String()+filepath.Ext(uploadRes.Filename))
				err = o.CopyObject(uploadRes.Url, dest)
				if err != nil {
					cosy.ErrHandler(c, err)
					return
				}
				upload = &model.Upload{
					Name: uploadRes.Filename,
					Path: dest,
					Size: uploadRes.Size,
					MIME: uploadRes.MIME,
					To:   "avatar",
				}
				ctx.AddSelectedFields("avatar_id")
			}

			user := api.CurrentUser(c)
			db := cosy.UseDB(context.Background())

			err = db.Transaction(func(tx *gorm.DB) error {
				avatarID := uint64(0)
				if upload != nil {
					err := tx.Create(upload).Error
					if err != nil {
						return err
					}

					err = tx.Model(&model.Upload{}).Where(
						query.Upload.Name.Eq(upload.Name),
						query.Upload.Path.Eq(upload.Path),
						query.Upload.To.Eq(upload.To),
					).First(upload).Error

					if err != nil {
						return err
					}

					// 将旧的头像标记为删除
					err = tx.Model(&model.Upload{}).Where(query.Upload.ID.Eq(user.AvatarID)).Update(
						"deleted_at",
						time.Now().Unix(),
					).Error
					if err != nil {
						return err
					}

					// OSS 删除失败不管
					if user.Avatar != nil && user.Avatar.Path != "" {
						_ = o.DeleteObject(user.Avatar.Path)
					}
					avatarID = upload.ID
				}

				err = tx.Model(&model.User{Model: model.Model{ID: user.ID}}).Select(ctx.GetSelectedFields()).
					Where(query.User.ID.Eq(user.ID)).Updates(&model.User{
					AvatarID: avatarID,
					Name:     ctx.Model.Name,
					Phone:    ctx.Model.Phone,
					Email:    ctx.Model.Email,
				}).Error
				if err != nil {
					return err
				}

				// 删除缓存
				redis.Del(api.BuildUserKey(user.ID))

				return nil
			})
			if err != nil {
				cosy.ErrHandler(c, err)
				return
			}

			u := query.User
			user, _ = u.Where(u.ID.Eq(user.ID)).Preload(u.Avatar).First()

			c.JSON(http.StatusOK, user)
		})

}
