package upload

import (
	"errors"
	"net/http"
	"net/url"
	"path"
	"time"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/internal/oss"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"git.uozi.org/uozi/potato-billing-api/settings"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/uozi-tech/cosy"
)

var trustTo = []string{
	"intelligent_agent",
	"role_template",
	"voiceprint",
	"post",
	"post_category",
	"icon",
	"qr_code",
	"img",
	"ota",
	"",
}

func MediaUpload(c *gin.Context) {
	user := api.CurrentUser(c)
	// check trust category first
	to := c.PostForm("to")
	if !lo.Contains(trustTo, to) {
		c.JSON(http.StatusNotAcceptable, gin.H{
			"message": "target category is not trusted",
			"to":      to,
		})
		return
	}

	dir := path.Join(time.Now().Format("2006/01/02"))

	o, err := oss.NewOSS()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	res, err := o.UploadSingleFile(c, dir)
	if err != nil {
		if errors.Is(err, oss.ErrClientUploadFileType) {
			c.JSON(http.StatusNotAcceptable, gin.H{
				"message": oss.ErrClientUploadFileType.Error(),
			})
			return
		}
		cosy.ErrHandler(c, err)
		return
	}

	// new record to the database
	upload := &model.Upload{
		UserId: user.ID,
		Path:   res.Url,
		Size:   res.Size,
		MIME:   res.MIME,
		Name:   res.Filename,
		To:     to,
	}

	u := query.Upload
	if err = u.Create(upload); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	upload.OriginalPath = res.Url

	fullUrl, err := url.JoinPath(settings.OssSettings.BaseUrl, res.Url)
	if err == nil {
		upload.Path = fullUrl
	}

	upload.User = user

	c.JSON(http.StatusOK, upload)
}

func GetUploadUrl(c *gin.Context) {
	relativePath := c.Query("path")
	if relativePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "path is required",
		})
		return
	}
	fullUrl, err := url.JoinPath(settings.OssSettings.BaseUrl, relativePath)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"url": fullUrl,
	})
}

const TempDir = "temp"

func UploadToTemp(c *gin.Context) {
	o, err := oss.NewOSS()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	res, err := o.UploadSingleFile(c, TempDir)
	if err != nil {
		return
	}
	c.JSON(http.StatusOK, res)
}
