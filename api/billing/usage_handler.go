package billing

import (
	"errors"
	"net/http"

	"git.uozi.org/uozi/potato-billing-api/internal/billing"
	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/gin-gonic/gin"
	"github.com/uozi-tech/cosy"
	"github.com/uozi-tech/cosy/logger"
	"gorm.io/gorm"
)

// UsageHistoryResponse 用量历史响应结构
type UsageHistoryResponse struct {
	Total int64            `json:"total"`
	Items []model.UsageLog `json:"items"`
}

// GetKeyUsageHistory 获取用量历史
func GetKeyUsageHistory(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	cosy.Core[model.UsageLog](c).SetValidRules(gin.H{
		"module":     "in",
		"start_time": "gte",
		"end_time":   "lte",
	}).GormScope(func(tx *gorm.DB) *gorm.DB {
		return tx.Where("api_key = ?", key)
	}).PagingList()
}

// GetKeyUsageStats 获取用量统计
func GetKeyUsageStats(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		cosy.ErrHandler(c, errors.New("Key参数必需"))
		return
	}

	period := c.DefaultQuery("period", "month")

	// 获取统计服务实例
	statService := billing.GetStatServiceInstance()
	if statService == nil {
		logger.Error("获取统计服务失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "统计服务不可用"})
		return
	}

	// 调用统计服务获取数据
	stats, err := statService.GetUsageStats(c.Request.Context(), key, period)
	if err != nil {
		logger.Error("获取使用量统计失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取统计数据失败"})
		return
	}

	c.JSON(http.StatusOK, stats)
}
