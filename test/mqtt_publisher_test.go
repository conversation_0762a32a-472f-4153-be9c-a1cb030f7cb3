package test

// import (
// 	"context"
// 	"encoding/json"
// 	"net/url"
// 	"sync"
// 	"testing"
// 	"time"

// 	"git.uozi.org/uozi/potato-billing-api/internal/billing"
// 	"github.com/eclipse/paho.golang/autopaho"
// 	"github.com/eclipse/paho.golang/paho"
// 	"github.com/stretchr/testify/assert"
// )

// // TestMQTTPublisher 测试MQTT消息发布
// func TestMQTTPublisher(t *testing.T) {
// 	// 跳过测试如果没有MQTT服务器
// 	if testing.Short() {
// 		t.Skip("跳过MQTT集成测试")
// 	}

// 	// 创建MQTT客户端
// 	client, err := createTestMQTTClient()
// 	assert.NoError(t, err)
// 	defer client.Disconnect(context.Background())

// 	// 等待连接建立
// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()

// 	err = client.AwaitConnection(ctx)
// 	assert.NoError(t, err, "MQTT连接应该成功")

// 	// 发送测试用量报告
// 	report := billing.UsageReport{
// 		APIKey:       "test-key-1",
// 		Module:    "llm",
// 		Model:     "gpt-3.5-turbo",
// 		Usage:     1500,
// 		Metadata:  map[string]interface{}{"user_id": "test-user"},
// 		Timestamp: time.Now().UnixMilli(),
// 	}

// 	payload, err := json.Marshal(report)
// 	assert.NoError(t, err)

// 	// 发布消息
// 	_, err = client.Publish(ctx, &paho.Publish{
// 		QoS:     1,
// 		Topic:   "billing/report",
// 		Payload: payload,
// 	})

// 	assert.NoError(t, err, "发布用量报告应该成功")

// 	t.Logf("成功发送用量报告: %+v", report)
// }

// // TestMQTTMessageReception 测试MQTT消息接收（需要同时启动服务端）
// func TestMQTTMessageReception(t *testing.T) {
// 	if testing.Short() {
// 		t.Skip("跳过MQTT集成测试")
// 	}

// 	// 创建消息接收器来验证消息传递
// 	receivedMessages := make(chan billing.UsageReport, 10)
// 	var messageCount int32
// 	var mutex sync.Mutex

// 	// 创建订阅客户端
// 	subscriber, err := createTestSubscriberClient(func(report billing.UsageReport) {
// 		mutex.Lock()
// 		defer mutex.Unlock()
// 		receivedMessages <- report
// 		messageCount++
// 		t.Logf("接收到消息: module=%s, usage=%d", report.Module, report.Usage)
// 	})
// 	assert.NoError(t, err)
// 	defer subscriber.Disconnect(context.Background())

// 	// 创建发布客户端
// 	publisher, err := createTestMQTTClient()
// 	assert.NoError(t, err)
// 	defer publisher.Disconnect(context.Background())

// 	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
// 	defer cancel()

// 	// 等待两个客户端都连接
// 	err = subscriber.AwaitConnection(ctx)
// 	assert.NoError(t, err, "订阅客户端连接应该成功")

// 	err = publisher.AwaitConnection(ctx)
// 	assert.NoError(t, err, "发布客户端连接应该成功")

// 	// 等待订阅完成
// 	time.Sleep(2 * time.Second)

// 	// 发送测试消息
// 	testReports := []billing.UsageReport{
// 		{
// 			APIKey:       "test-key-1",
// 			Module:    "llm",
// 			Model:     "gpt-3.5-turbo",
// 			Usage:     1000,
// 			Timestamp: time.Now().UnixMilli(),
// 		},
// 		{
// 			APIKey:       "test-key-2",
// 			Module:    "tts",
// 			Model:     "whisper",
// 			Usage:     500,
// 			Timestamp: time.Now().UnixMilli(),
// 		},
// 	}

// 	// 发布消息
// 	for i, report := range testReports {
// 		payload, err := json.Marshal(report)
// 		assert.NoError(t, err)

// 		_, err = publisher.Publish(ctx, &paho.Publish{
// 			QoS:     1,
// 			Topic:   "billing/report",
// 			Payload: payload,
// 		})
// 		assert.NoError(t, err)

// 		t.Logf("发送第%d个消息: %+v", i+1, report)
// 		time.Sleep(500 * time.Millisecond) // 添加延迟
// 	}

// 	// 等待消息接收
// 	timeout := time.After(10 * time.Second)
// 	receivedCount := 0

// receiveLoop:
// 	for receivedCount < len(testReports) {
// 		select {
// 		case received := <-receivedMessages:
// 			receivedCount++
// 			t.Logf("验证接收消息 %d/%d: %+v", receivedCount, len(testReports), received)
// 		case <-timeout:
// 			t.Errorf("接收消息超时，期望 %d 个，实际接收 %d 个", len(testReports), receivedCount)
// 			break receiveLoop
// 		}
// 	}

// 	mutex.Lock()
// 	finalCount := messageCount
// 	mutex.Unlock()

// 	assert.Equal(t, int32(len(testReports)), finalCount, "应该接收到所有发送的消息")
// 	t.Logf("消息传递测试完成，成功接收 %d 个消息", finalCount)
// }

// // TestBatchUsageReports 测试批量用量报告
// func TestBatchUsageReports(t *testing.T) {
// 	if testing.Short() {
// 		t.Skip("跳过MQTT集成测试")
// 	}

// 	client, err := createTestMQTTClient()
// 	assert.NoError(t, err)
// 	defer client.Disconnect(context.Background())

// 	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
// 	defer cancel()

// 	err = client.AwaitConnection(ctx)
// 	assert.NoError(t, err)

// 	// 发送多个用量报告
// 	reports := []billing.UsageReport{
// 		{
// 			APIKey:       "test-key-1",
// 			Module:    "llm",
// 			Model:     "gpt-3.5-turbo",
// 			Usage:     1000,
// 			Timestamp: time.Now().UnixMilli(),
// 		},
// 		{
// 			APIKey:       "test-key-1",
// 			Module:    "tts",
// 			Model:     "whisper",
// 			Usage:     500,
// 			Timestamp: time.Now().UnixMilli(),
// 		},
// 		{
// 			APIKey:       "test-key-2",
// 			Module:    "asr",
// 			Model:     "speech-to-text",
// 			Usage:     800,
// 			Timestamp: time.Now().UnixMilli(),
// 		},
// 	}

// 	var publishWg sync.WaitGroup
// 	for i, report := range reports {
// 		publishWg.Add(1)
// 		go func(index int, r billing.UsageReport) {
// 			defer publishWg.Done()

// 			payload, err := json.Marshal(r)
// 			assert.NoError(t, err)

// 			_, err = client.Publish(ctx, &paho.Publish{
// 				QoS:     1,
// 				Topic:   "billing/report",
// 				Payload: payload,
// 			})

// 			assert.NoError(t, err, "发布第%d个用量报告应该成功", index+1)
// 		}(i, report)

// 		// 添加小延迟避免消息过快
// 		time.Sleep(100 * time.Millisecond)
// 	}

// 	publishWg.Wait()
// 	t.Logf("成功发送 %d 个用量报告", len(reports))
// }

// // createTestMQTTClient 创建测试用的MQTT客户端
// func createTestMQTTClient() (*autopaho.ConnectionManager, error) {
// 	// 解析MQTT URL
// 	u, err := url.Parse("mqtt://localhost:1883")
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 测试用MQTT配置
// 	cliCfg := autopaho.ClientConfig{
// 		ServerUrls: []*url.URL{u},
// 		KeepAlive:  20,
// 		ClientConfig: paho.ClientConfig{
// 			ClientID: "test-publisher-" + generateTestID(),
// 		},
// 		OnConnectionUp: func(cm *autopaho.ConnectionManager, connAck *paho.Connack) {
// 			// 连接成功回调
// 		},
// 		OnConnectError: func(err error) {
// 			// 连接错误回调
// 		},
// 	}

// 	return autopaho.NewConnection(context.Background(), cliCfg)
// }

// // createTestSubscriberClient 创建测试用的订阅客户端
// func createTestSubscriberClient(messageHandler func(billing.UsageReport)) (*autopaho.ConnectionManager, error) {
// 	u, err := url.Parse("mqtt://localhost:1883")
// 	if err != nil {
// 		return nil, err
// 	}

// 	cliCfg := autopaho.ClientConfig{
// 		ServerUrls: []*url.URL{u},
// 		KeepAlive:  20,
// 		OnConnectionUp: func(cm *autopaho.ConnectionManager, connAck *paho.Connack) {
// 			// 订阅用量上报主题
// 			if _, err := cm.Subscribe(context.Background(), &paho.Subscribe{
// 				Subscriptions: []paho.SubscribeOptions{
// 					{Topic: "billing/report", QoS: 1},
// 				},
// 			}); err != nil {
// 				// 订阅失败处理
// 			}
// 		},
// 		OnConnectError: func(err error) {
// 			// 连接错误回调
// 		},
// 		ClientConfig: paho.ClientConfig{
// 			ClientID: "test-subscriber-" + generateTestID(),
// 			OnPublishReceived: []func(paho.PublishReceived) (bool, error){
// 				func(pr paho.PublishReceived) (bool, error) {
// 					if pr.Packet.Topic == "billing/report" {
// 						var report billing.UsageReport
// 						if err := json.Unmarshal(pr.Packet.Payload, &report); err == nil {
// 							messageHandler(report)
// 						}
// 					}
// 					return true, nil
// 				},
// 			},
// 		},
// 	}

// 	return autopaho.NewConnection(context.Background(), cliCfg)
// }

// // generateTestID 生成测试ID
// func generateTestID() string {
// 	return time.Now().Format("20060102150405")
// }

// // BenchmarkMQTTPublish 性能测试
// func BenchmarkMQTTPublish(b *testing.B) {
// 	if testing.Short() {
// 		b.Skip("跳过MQTT性能测试")
// 	}

// 	client, err := createTestMQTTClient()
// 	if err != nil {
// 		b.Fatal(err)
// 	}
// 	defer client.Disconnect(context.Background())

// 	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
// 	defer cancel()

// 	err = client.AwaitConnection(ctx)
// 	if err != nil {
// 		b.Fatal(err)
// 	}

// 	report := billing.UsageReport{
// 		APIKey:       "benchmark-key",
// 		Module:    "llm",
// 		Model:     "gpt-3.5-turbo",
// 		Usage:     100,
// 		Timestamp: time.Now().UnixMilli(),
// 	}

// 	payload, err := json.Marshal(report)
// 	if err != nil {
// 		b.Fatal(err)
// 	}

// 	b.ResetTimer()
// 	b.RunParallel(func(pb *testing.PB) {
// 		for pb.Next() {
// 			_, err := client.Publish(ctx, &paho.Publish{
// 				QoS:     1,
// 				Topic:   "billing/report",
// 				Payload: payload,
// 			})
// 			if err != nil {
// 				b.Error(err)
// 			}
// 		}
// 	})
// }
