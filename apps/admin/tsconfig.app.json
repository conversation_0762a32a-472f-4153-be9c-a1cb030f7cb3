{"extends": "../../tsconfig.base.json", "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@ui": ["../../packages/ui/src"], "@ui/*": ["../../packages/ui/src/*"], "@curd": ["../../packages/curd/src"], "@curd/*": ["../../packages/curd/src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "../../packages/ui/**/*.vue"]}