/**
 * 业务相关常量定义
 */

import { 
  SERVICE_MODULE, 
  USER_STATUS, 
  API_KEY_STATUS, 
  QUOTA_STATUS, 
  RECHARGE_STATUS, 
  RECHARGE_TYPE, 
  QUOTA_TYPE 
} from './common'

// 模块名称映射
export const MODULE_NAMES = {
  [SERVICE_MODULE.LLM]: 'LLM',
  [SERVICE_MODULE.TTS]: 'TTS语音合成',
  [SERVICE_MODULE.ASR]: 'ASR语音识别',
} as const

// 服务名称映射（简化版）
export const SERVICE_NAMES = {
  [SERVICE_MODULE.LLM]: 'LLM服务',
  [SERVICE_MODULE.TTS]: 'TTS服务',
  [SERVICE_MODULE.ASR]: 'ASR服务',
} as const

// 用户状态文本映射
export const USER_STATUS_TEXT = {
  [USER_STATUS.ACTIVE]: '激活',
  [USER_STATUS.DISABLED]: '禁用',
} as const

// API Key 状态文本映射
export const API_KEY_STATUS_TEXT = {
  [API_KEY_STATUS.OK]: '正常',
  [API_KEY_STATUS.BLOCKED]: '阻止',
} as const

// 配额状态文本映射
export const QUOTA_STATUS_TEXT = {
  [QUOTA_STATUS.ACTIVE]: '活跃',
  [QUOTA_STATUS.EXPIRED]: '已过期',
  [QUOTA_STATUS.EXHAUSTED]: '已用完',
  [QUOTA_STATUS.DISABLED]: '已禁用',
} as const

// 充值状态文本映射
export const RECHARGE_STATUS_TEXT = {
  [RECHARGE_STATUS.COMPLETED]: '已完成',
  [RECHARGE_STATUS.PENDING]: '待处理',
  [RECHARGE_STATUS.FAILED]: '失败',
  [RECHARGE_STATUS.CANCELLED]: '已取消',
} as const

// 充值类型标签映射
export const RECHARGE_TYPE_LABELS = {
  [RECHARGE_TYPE.ADMIN]: '管理员充值',
  [RECHARGE_TYPE.ALIPAY]: '支付宝',
  [RECHARGE_TYPE.WECHAT]: '微信支付',
  [RECHARGE_TYPE.BANK]: '银行转账',
} as const

// 充值类型颜色映射
export const RECHARGE_TYPE_COLORS = {
  [RECHARGE_TYPE.ADMIN]: 'bg-purple-100 text-purple-800',
  [RECHARGE_TYPE.ALIPAY]: 'bg-blue-100 text-blue-800',
  [RECHARGE_TYPE.WECHAT]: 'bg-green-100 text-green-800',
  [RECHARGE_TYPE.BANK]: 'bg-orange-100 text-orange-800',
} as const

// 配额类型标签映射
export const QUOTA_TYPE_LABELS = {
  [QUOTA_TYPE.ADMIN]: '管理员赠送',
  [QUOTA_TYPE.PURCHASE]: '购买获得',
  [QUOTA_TYPE.PROMOTION]: '促销活动',
  [QUOTA_TYPE.GIFT]: '系统赠送',
} as const

// 模块颜色映射
export const MODULE_COLORS = {
  [SERVICE_MODULE.LLM]: {
    bg: 'bg-blue-500',
    text: 'text-blue-600',
    light: 'bg-blue-50',
  },
  [SERVICE_MODULE.TTS]: {
    bg: 'bg-green-500',
    text: 'text-green-600',
    light: 'bg-green-50',
  },
  [SERVICE_MODULE.ASR]: {
    bg: 'bg-purple-500',
    text: 'text-purple-600',
    light: 'bg-purple-50',
  },
} as const

// 单位映射
export const UNIT_NAMES = {
  token: 'tokens',
  character: '字符',
  seconds: '秒',
  second: '秒',
} as const

// 表单选项配置
export const FORM_OPTIONS = {
  // 服务模块选项
  SERVICE_MODULES: [
    { label: SERVICE_NAMES[SERVICE_MODULE.LLM], value: SERVICE_MODULE.LLM },
    { label: SERVICE_NAMES[SERVICE_MODULE.TTS], value: SERVICE_MODULE.TTS },
    { label: SERVICE_NAMES[SERVICE_MODULE.ASR], value: SERVICE_MODULE.ASR },
  ] as const,

  // 用户状态选项
  USER_STATUS: [
    { label: USER_STATUS_TEXT[USER_STATUS.ACTIVE], value: USER_STATUS.ACTIVE },
    { label: USER_STATUS_TEXT[USER_STATUS.DISABLED], value: USER_STATUS.DISABLED },
  ] as const,
  
  // API Key 状态选项
  API_KEY_STATUS: [
    { label: API_KEY_STATUS_TEXT[API_KEY_STATUS.OK], value: API_KEY_STATUS.OK },
    { label: API_KEY_STATUS_TEXT[API_KEY_STATUS.BLOCKED], value: API_KEY_STATUS.BLOCKED },
  ] as const,
  
  // 货币选项
  CURRENCY: [
    { label: '人民币 (CNY)', value: 'CNY' },
    { label: '美元 (USD)', value: 'USD' },
  ] as const,

  // 充值类型选项
  RECHARGE_TYPES: [
    { label: RECHARGE_TYPE_LABELS[RECHARGE_TYPE.ADMIN], value: RECHARGE_TYPE.ADMIN },
    { label: RECHARGE_TYPE_LABELS[RECHARGE_TYPE.ALIPAY], value: RECHARGE_TYPE.ALIPAY },
    { label: RECHARGE_TYPE_LABELS[RECHARGE_TYPE.WECHAT], value: RECHARGE_TYPE.WECHAT },
    { label: RECHARGE_TYPE_LABELS[RECHARGE_TYPE.BANK], value: RECHARGE_TYPE.BANK },
  ] as const,
} as const

// 成功消息模板
export const SUCCESS_MESSAGES = {
  USER_CREATED: '用户创建成功！',
  USER_UPDATED: '用户信息更新成功！',
  USER_DELETED: '用户删除成功！',
  USER_BATCH_DELETED: '批量删除完成！',
  
  API_KEY_CREATED: 'API Key创建成功！',
  API_KEY_UPDATED: 'API Key更新成功！',
  API_KEY_DELETED: 'API Key删除成功！',
  API_KEY_BATCH_DELETED: '批量删除完成！',
  
  QUOTA_CREATED: '资源包创建成功！',
  QUOTA_UPDATED: '资源包更新成功！',
  
  RECHARGE_CREATED: '充值成功！',
  
  PRICING_CREATED: '价格规则创建成功！',
  PRICING_UPDATED: '价格规则更新成功！',
  PRICING_DELETED: '价格规则删除成功！',
  PRICING_BATCH_DELETED: '批量删除完成！',
} as const

// 确认消息模板
export const CONFIRM_MESSAGES = {
  DELETE_USER: '确定要删除这个用户吗？删除后无法恢复！',
  BATCH_DELETE_USERS: '确定要删除选中的用户吗？此操作不可撤销！',
  
  DELETE_API_KEY: '确定要删除这个API Key吗？删除后无法恢复！',
  BATCH_DELETE_API_KEYS: '确定要删除选中的API Key吗？此操作不可撤销！',
  
  DELETE_PRICING: '确定要删除这条价格规则吗？删除后将无法恢复！',
  BATCH_DELETE_PRICING: '确定要删除选中的价格规则吗？此操作不可撤销！',
} as const

// 计费说明文本
export const BILLING_DESCRIPTIONS = {
  MIXED: '混合计费：优先使用资源包，资源包用完后扣除用户余额',
  QUOTA_ONLY: '资源包计费：仅使用配置的资源包',
  BALANCE_ONLY: '余额计费：直接扣除用户余额',
  NO_BILLING: '无可用计费方式：请添加资源包或为用户充值',
  
  API_KEY_SYSTEM: '系统支持两种计费方式：①资源包计费（优先）- 从配置的资源包中扣减用量；②余额计费（兜底）- 当资源包不足时，从用户余额中扣费。',
  RECHARGE_SYSTEM: '用户余额用于在资源包不足时进行计费。管理员可以直接为用户充值，用户也可以通过第三方支付完成充值。',
} as const
