/**
 * UI 相关常量定义
 */

// 表格配置
export const TABLE_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_ROWS_PER_PAGE: 100,
} as const

// 表单验证
export const FORM_VALIDATION = {
  MIN_PASSWORD_LENGTH: 6,
  MAX_PASSWORD_LENGTH: 50,
  MIN_NAME_LENGTH: 2,
  MAX_NAME_LENGTH: 50,
  EMAIL_PATTERN: /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/,
  PHONE_PATTERN: /^1[3-9]\d{9}$/,
} as const

// 文件上传限制
export const FILE_UPLOAD = {
  MAX_SIZE_MB: 10,
  ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  ALLOWED_DOCUMENT_TYPES: ['pdf', 'doc', 'docx', 'txt'],
  ALLOWED_EXTENSIONS: 'jpg,jpeg,png,gif,webp,pdf,doc,docx,txt',
} as const

// 动画和过渡时间
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  TOAST_DURATION: 5000,
  TOOLTIP_DELAY: 500,
} as const

// 布局断点
export const BREAKPOINTS = {
  'SM': 640,
  'MD': 768,
  'LG': 1024,
  'XL': 1280,
  '2XL': 1536,
} as const

// 颜色主题
export const UI_COLORS = {
  PRIMARY: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    900: '#1e3a8a',
  },
  SUCCESS: {
    50: '#f0fdf4',
    100: '#dcfce7',
    500: '#22c55e',
    600: '#16a34a',
    900: '#14532d',
  },
  WARNING: {
    50: '#fffbeb',
    100: '#fef3c7',
    500: '#f59e0b',
    600: '#d97706',
    900: '#78350f',
  },
  DANGER: {
    50: '#fef2f2',
    100: '#fee2e2',
    500: '#ef4444',
    600: '#dc2626',
    900: '#7f1d1d',
  },
  GRAY: {
    50: '#f9fafb',
    100: '#f3f4f6',
    500: '#6b7280',
    600: '#4b5563',
    900: '#111827',
  },
} as const

// 图标大小
export const ICON_SIZES = {
  XS: 'w-3 h-3',
  SM: 'w-4 h-4',
  MD: 'w-5 h-5',
  LG: 'w-6 h-6',
  XL: 'w-8 h-8',
} as const

// 间距
export const SPACING = {
  XS: 'gap-1',
  SM: 'gap-2',
  MD: 'gap-4',
  LG: 'gap-6',
  XL: 'gap-8',
} as const

// 圆角
export const BORDER_RADIUS = {
  NONE: 'rounded-none',
  SM: 'rounded-sm',
  MD: 'rounded-md',
  LG: 'rounded-lg',
  XL: 'rounded-xl',
  FULL: 'rounded-full',
} as const

// 阴影
export const SHADOWS = {
  NONE: 'shadow-none',
  SM: 'shadow-sm',
  MD: 'shadow-md',
  LG: 'shadow-lg',
  XL: 'shadow-xl',
} as const
