# 前端代码重构指南

本文档记录了对 `apps/admin/src/` 目录下前端代码的重构优化过程和结果。

## 重构概述

本次重构主要包含以下几个方面：

1. **提取公共函数模块** - 将重复使用的函数提取到公共模块
2. **组件拆分优化** - 将大型组件拆分为多个小的子组件
3. **常量提取与组织** - 将硬编码值提取到常量文件
4. **代码质量优化** - 改善 TypeScript 类型定义和代码风格

## 新增文件结构

### 工具函数 (utils/)

- `utils/format.ts` - 格式化相关函数
  - `formatCurrency()` - 货币格式化
  - `formatDate()` - 日期时间格式化
  - `formatNumber()` - 数字格式化
  - `formatFileSize()` - 文件大小格式化
  - `formatUsageWithUnit()` - 带单位的使用量格式化

- `utils/business.ts` - 业务逻辑相关函数
  - `getModuleName()` - 获取模块名称
  - `getStatusColor()` - 获取状态颜色
  - `maskApiKey()` - 遮蔽 API Key
  - `copyToClipboard()` - 复制到剪贴板

- `utils/index.ts` - 工具函数统一导出

### 组合式函数 (composables/)

- `composables/useCopy.ts` - 复制功能 Hook
  - `useCopy()` - 多项复制状态管理
  - `useSingleCopy()` - 单项复制状态管理

### 常量定义 (constants/)

- `constants/common.ts` - 通用常量
  - 分页配置、时间间隔、状态值等

- `constants/business.ts` - 业务常量
  - 模块名称映射、状态文本、表单选项等

- `constants/ui.ts` - UI 相关常量
  - 表格配置、动画时间、颜色主题等

- `constants/index.ts` - 常量统一导出

### 类型定义 (types/)

- `types/common.ts` - 通用类型定义
  - 分页、表单、API 响应等类型

### 组件拆分

#### billing/keys/components/

将原本 1104 行的 `detail.vue` 拆分为以下子组件：

- `KeyBasicInfo.vue` - Key 基本信息展示
- `QuotaManagement.vue` - 资源包管理
- `UsageStats.vue` - 用量统计
- `UsageHistory.vue` - 使用历史
- `QuotaEditDialog.vue` - 配额编辑弹窗
- `index.ts` - 组件统一导出

## 重构前后对比

### 代码行数优化

| 文件 | 重构前 | 重构后 | 减少 |
|------|--------|--------|------|
| `billing/keys/detail.vue` | 1104 行 | ~300 行 | ~800 行 |
| `dashboard/index.vue` | 646 行 | ~500 行 | ~150 行 |
| `billing/keys/index.vue` | 481 行 | ~350 行 | ~130 行 |

### 重复代码消除

消除了以下重复函数：
- `formatCurrency()` - 在 8+ 个组件中重复
- `formatDate()` - 在 7+ 个组件中重复
- `getModuleName()` - 在 5+ 个组件中重复
- `copyApiKey()` - 在 3+ 个组件中重复

### 常量提取

提取了以下硬编码值：
- 分页大小：`20` → `PAGINATION.DEFAULT_PAGE_SIZE`
- 自动刷新间隔：`30000` → `TIME_INTERVALS.AUTO_REFRESH`
- 用户状态：`1/0` → `USER_STATUS.ACTIVE/DISABLED`
- API Key 状态：`'ok'/'blocked'` → `API_KEY_STATUS.OK/BLOCKED`

## 使用指南

### 导入工具函数

```typescript
// 导入格式化函数
import { formatCurrency, formatDate } from '@/utils'

// 导入业务函数
import { getModuleName, maskApiKey } from '@/utils'
```

### 导入常量

```typescript
// 导入通用常量
import { PAGINATION, TIME_INTERVALS } from '@/constants'

// 导入业务常量
import { MODULE_NAMES, SUCCESS_MESSAGES } from '@/constants'
```

### 使用组合式函数

```typescript
import { useCopy } from '@/composables/useCopy'

const { copyText, isCopied } = useCopy()

// 复制文本
await copyText('要复制的内容', 'unique-key')

// 检查是否已复制
const copied = isCopied('unique-key')
```

### 使用子组件

```vue
<template>
  <KeyBasicInfo 
    :key-info="keyInfo"
    :quota-stats="quotaStats"
  />
  
  <QuotaManagement 
    :key-info="keyInfo"
    @add-quota="handleAddQuota"
    @toggle-quota-status="handleToggleStatus"
  />
</template>

<script setup lang="ts">
import { KeyBasicInfo, QuotaManagement } from './components'
</script>
```

## 最佳实践

### 1. 函数提取原则

- 当函数在 2+ 个组件中重复使用时，应提取到公共模块
- 格式化函数放在 `utils/format.ts`
- 业务逻辑函数放在 `utils/business.ts`
- 状态管理逻辑使用 composables

### 2. 组件拆分原则

- 单个组件不超过 300 行代码
- 每个子组件有单一职责
- 保持组件间数据流清晰（props down, events up）
- 复用性高的组件放在 `components/` 目录

### 3. 常量管理原则

- 硬编码的数字、字符串都应提取为常量
- 按功能模块分类组织常量文件
- 使用 TypeScript 的 `as const` 确保类型安全
- 提供统一的导出入口

### 4. 类型定义原则

- 为所有公共函数和组件添加 TypeScript 类型
- 复用的类型定义放在 `types/` 目录
- 使用联合类型替代字符串字面量
- 为 API 响应定义明确的接口

## 注意事项

1. **向后兼容性** - 重构保持了所有现有功能不变
2. **性能优化** - 通过代码拆分减少了单个文件的体积
3. **可维护性** - 提高了代码的可读性和可维护性
4. **类型安全** - 增强了 TypeScript 类型检查

## 后续优化建议

1. **单元测试** - 为提取的公共函数编写单元测试
2. **文档完善** - 为复杂的业务逻辑添加详细注释
3. **性能监控** - 监控重构后的页面加载性能
4. **代码审查** - 定期审查新增代码是否遵循重构后的规范
