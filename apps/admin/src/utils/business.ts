/**
 * 业务相关工具函数
 * 提供业务逻辑相关的工具方法
 */

import type { Component } from 'vue'
import { ArrowDownRight, ArrowUpRight, Minus } from 'lucide-vue-next'

/**
 * 获取模块名称映射
 * @param module 模块标识
 */
export function getModuleName(module: string): string {
  const moduleNames: Record<string, string> = {
    llm: 'LLM',
    tts: 'TTS语音合成',
    asr: 'ASR语音识别',
  }
  return moduleNames[module] || module
}

/**
 * 获取服务名称映射（简化版）
 * @param service 服务标识
 */
export function getServiceName(service: string): string {
  const serviceNames: Record<string, string> = {
    llm: 'LLM服务',
    tts: 'TTS服务',
    asr: 'ASR服务',
  }
  return serviceNames[service] || service
}

/**
 * 获取状态颜色变体
 * @param status 状态值
 */
export function getStatusColor(status: string): 'default' | 'destructive' | 'secondary' | 'outline' {
  return status === 'ok' ? 'default' : 'destructive'
}

/**
 * 获取可用性颜色
 * @param available 是否可用
 */
export function getAvailabilityColor(available: boolean): 'default' | 'destructive' {
  return available ? 'default' : 'destructive'
}

/**
 * 获取配额状态变体
 * @param status 配额状态
 */
export function getQuotaStatusVariant(status: string): 'default' | 'secondary' | 'outline' | 'destructive' {
  switch (status) {
    case 'active':
      return 'default'
    case 'expired':
      return 'secondary'
    case 'exhausted':
      return 'outline'
    case 'disabled':
      return 'destructive'
    default:
      return 'secondary'
  }
}

/**
 * 获取充值状态变体
 * @param status 充值状态
 */
export function getRechargeStatusVariant(status: string): 'default' | 'secondary' | 'destructive' | 'outline' {
  switch (status) {
    case 'completed':
      return 'default'
    case 'pending':
      return 'secondary'
    case 'failed':
      return 'destructive'
    case 'cancelled':
      return 'outline'
    default:
      return 'secondary'
  }
}

/**
 * 获取充值状态文本
 * @param status 充值状态
 */
export function getRechargeStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    completed: '已完成',
    pending: '待处理',
    failed: '失败',
    cancelled: '已取消',
  }
  return statusMap[status] || status
}

/**
 * 获取充值类型标签
 * @param type 充值类型
 */
export function getRechargeTypeLabel(type: string): string {
  const types: Record<string, string> = {
    admin: '管理员充值',
    alipay: '支付宝',
    wechat: '微信支付',
    bank: '银行转账',
  }
  return types[type] || type
}

/**
 * 获取充值类型颜色类名
 * @param type 充值类型
 */
export function getRechargeTypeColor(type: string): string {
  const colors: Record<string, string> = {
    admin: 'bg-purple-100 text-purple-800',
    alipay: 'bg-blue-100 text-blue-800',
    wechat: 'bg-green-100 text-green-800',
    bank: 'bg-orange-100 text-orange-800',
  }
  return colors[type] || 'bg-gray-100 text-gray-800'
}

/**
 * 获取配额类型标签
 * @param type 配额类型
 */
export function getQuotaTypeLabel(type: string): string {
  const types: Record<string, string> = {
    admin: '管理员赠送',
    purchase: '购买获得',
    promotion: '促销活动',
    gift: '系统赠送',
  }
  return types[type] || type
}

/**
 * 获取趋势图标
 * @param growth 增长率
 */
export function getTrendIcon(growth: number): Component {
  if (growth > 0) return ArrowUpRight
  if (growth < 0) return ArrowDownRight
  return Minus
}

/**
 * 获取趋势颜色类名
 * @param growth 增长率
 */
export function getTrendColor(growth: number): string {
  if (growth > 0) return 'text-green-600'
  if (growth < 0) return 'text-red-600'
  return 'text-gray-600'
}

/**
 * 获取模块颜色样式类
 * @param module 模块名
 * @param isBackground 是否为背景色
 */
export function getModuleColorClass(module: string, isBackground = false): string {
  const colorMap: Record<string, string> = {
    llm: isBackground ? 'bg-blue-500' : 'bg-blue-500',
    tts: isBackground ? 'bg-green-500' : 'bg-green-500',
    asr: isBackground ? 'bg-purple-500' : 'bg-purple-500',
  }
  return colorMap[module] || (isBackground ? 'bg-gray-500' : 'bg-gray-500')
}

/**
 * 获取文件类型标签颜色
 * @param fileType 文件类型
 */
export function getFileTypeBadgeVariant(fileType: string): 'default' | 'secondary' | 'outline' {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
  const docTypes = ['pdf', 'doc', 'docx', 'txt']

  if (imageTypes.includes(fileType.toLowerCase())) {
    return 'default'
  } else if (docTypes.includes(fileType.toLowerCase())) {
    return 'secondary'
  } else {
    return 'outline'
  }
}

/**
 * 获取计费类型颜色
 * @param type 计费类型
 */
export function getBillingTypeColor(type: string): 'default' | 'secondary' {
  return type === 'quota' ? 'default' : 'secondary'
}

/**
 * 遮蔽 API Key
 * @param key API Key
 */
export function maskApiKey(key: string): string {
  if (!key) return ''
  return `${key.substring(0, 4)}****${key.substring(key.length - 4)}`
}

/**
 * 计算使用率
 * @param used 已使用量
 * @param total 总量
 */
export function getUsageRate(used: number, total: number): string {
  if (total === 0) return '0'
  return ((used / total) * 100).toFixed(2)
}

/**
 * 检查配额是否过期
 * @param expiresAt 过期时间戳
 */
export function isQuotaExpired(expiresAt?: number): boolean {
  if (!expiresAt) return false
  return new Date(expiresAt).getTime() < Date.now()
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (error) {
    console.error('复制失败:', error)
    return false
  }
}
