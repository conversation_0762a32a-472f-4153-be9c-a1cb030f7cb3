import type { RouteRecordRaw } from 'vue-router'
import { Home } from 'lucide-vue-next'

export const PATH_DASHBOARD = '/dashboard'

export const dashboardRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: PATH_DASHBOARD,
  },
  {
    path: PATH_DASHBOARD,
    name: 'Dashboard',
    component: () => import('@/views/dashboard/index.vue'),
    meta: {
      title: '系统概览',
      icon: Home,
      requiresAuth: true,
      showInMenu: true,
    },
  },
]
