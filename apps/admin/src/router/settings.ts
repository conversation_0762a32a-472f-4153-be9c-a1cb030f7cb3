import type { RouteRecordRaw } from 'vue-router'
import { Settings } from 'lucide-vue-next'

export const PATH_SETTINGS = '/settings'

export const settingsRoutes: RouteRecordRaw[] = [
  {
    path: PATH_SETTINGS,
    redirect: '/settings/general',
    name: 'Settings',
    meta: {
      title: '系统设置',
      requiresAuth: true,
      icon: Settings,
      showInMenu: true,
    },
    children: [
      {
        path: 'general',
        name: 'SettingsGeneral',
        component: () => import('@/views/settings/general.vue'),
        meta: {
          title: '通用设置',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: 'auth',
        name: 'SettingsAuth',
        component: () => import('@/views/settings/auth.vue'),
        meta: {
          title: '认证设置',
          requiresAuth: true,
          showInMenu: true,
        },
      },
    ],
  },
]
