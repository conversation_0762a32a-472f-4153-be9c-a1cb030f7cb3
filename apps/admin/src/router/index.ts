import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/store/user'
import { authRoutes, PATH_LOGIN } from './auth'
import { billingRoutes } from './billing'
import { dashboardRoutes } from './dashboard'
import { settingsRoutes } from './settings'
import { uploadsRoutes } from './uploads'
import { userRoutes } from './user'

export const menuRoutes = [
  ...dashboardRoutes,
  ...userRoutes,
  ...billingRoutes,
  ...uploadsRoutes,
  ...settingsRoutes,
]

const router = createRouter({
  history: createWebHistory(),
  routes: [{
    path: '/',
    redirect: '/dashboard',
    component: () => import('@/layouts/MainLayout.vue'),
    children: menuRoutes,
  }, ...authRoutes,
  // 404路由应该放在最后
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面未找到',
      hideInMenu: true,
    },
  }],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 无需认证的路由
  const publicPaths = [PATH_LOGIN]

  // 检查是否是公开路由
  const isPublicPath = publicPaths.includes(to.path)

  // 从localStorage恢复token
  if (!userStore.token) {
    const savedToken = localStorage.getItem('auth_token')
    if (savedToken) {
      userStore.setToken(savedToken)
    }
  }

  // 如果是公开路由，直接通过
  if (isPublicPath) {
    // 如果已经登录，重定向到主页
    if (userStore.token && to.path === PATH_LOGIN) {
      next('/dashboard')
    }
    else {
      next()
    }
    return
  }

  // 检查是否已登录
  if (!userStore.token) {
    // 未登录，重定向到登录页
    next({ path: PATH_LOGIN, query: { redirect: to.fullPath } })
    return
  }

  // 检查路由是否需要认证
  if (to.meta?.requiresAuth && !userStore.token) {
    next({ path: PATH_LOGIN, query: { redirect: to.fullPath } })
    return
  }

  // TODO: 添加权限检查
  // if (to.meta?.permissions && !hasPermissions(to.meta.permissions)) {
  //   next('/403')
  //   return
  // }

  next()
})

export { router }
