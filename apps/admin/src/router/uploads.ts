import type { RouteRecordRaw } from 'vue-router'
import { Upload } from 'lucide-vue-next'

export const PATH_UPLOADS = '/uploads'

export const uploadsRoutes: RouteRecordRaw[] = [
  {
    path: PATH_UPLOADS,
    name: 'Uploads',
    component: () => import('@/views/uploads/index.vue'),
    meta: {
      title: '文件管理',
      icon: Upload,
      requiresAuth: true,
      showInMenu: true,
    },
  },
]
