import type { RouteRecordRaw } from 'vue-router'
import { DollarSign } from 'lucide-vue-next'

export const PATH_BILLING = '/billing'

export const billingRoutes: RouteRecordRaw[] = [
  {
    path: PATH_BILLING,
    redirect: '/billing/keys',
    meta: {
      title: '计费管理',
      requiresAuth: true,
      icon: DollarSign,
      showInMenu: true,
    },
    children: [
      {
        path: 'keys',
        name: 'BillingKeys',
        component: () => import('@/views/billing/keys/index.vue'),
        meta: {
          title: 'API Key',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: 'keys/:key',
        name: 'BillingKeyDetail',
        component: () => import('@/views/billing/keys/detail.vue'),
        meta: {
          title: '详情',
          requiresAuth: true,
        },
      },
      {
        path: 'keys/:key/stats',
        name: 'BillingKeyStats',
        component: () => import('@/views/billing/keys/stats.vue'),
        meta: {
          title: '用量统计',
          requiresAuth: true,
        },
      },
      {
        path: 'pricing',
        name: 'BillingPricing',
        component: () => import('@/views/billing/pricing/index.vue'),
        meta: {
          title: '计费规则',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: 'recharge',
        name: 'BillingRecharge',
        component: () => import('@/views/billing/recharge/index.vue'),
        meta: {
          title: '充值管理',
          requiresAuth: true,
          showInMenu: true,
        },
      },
      {
        path: 'quota',
        name: 'QuotaPackage',
        component: () => import('@/views/billing/quota/index.vue'),
        meta: {
          title: '资源包管理',
          requiresAuth: true,
          showInMenu: true,
        },
      },
    ],
  },
]
