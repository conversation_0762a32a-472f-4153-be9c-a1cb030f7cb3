import type { RouteRecordRaw } from 'vue-router'
import { Users } from 'lucide-vue-next'

export const PATH_USERS = '/users'

export const userRoutes: RouteRecordRaw[] = [
  {
    path: PATH_USERS,
    name: 'Users',
    component: () => import('@/views/users/index.vue'),
    meta: {
      title: '用户管理',
      icon: Users,
      requiresAuth: true,
      showInMenu: true,
    },
  },
  {
    path: '/users/:id',
    name: 'UserDetail',
    component: () => import('@/views/users/detail.vue'),
    meta: {
      title: '用户详情',
      requiresAuth: true,
    },
  },
]
