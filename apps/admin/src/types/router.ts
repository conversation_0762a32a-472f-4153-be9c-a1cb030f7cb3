import type { Component } from 'vue'

// 扩展Vue Router的路由meta类型
declare module 'vue-router' {
  interface RouteMeta {
    /** 页面标题 */
    title?: string
    /** 菜单图标名称 */
    icon?: Component
    /** 是否需要认证 */
    requiresAuth?: boolean
    /** 在菜单中隐藏 */
    showInMenu?: boolean
    /** 权限代码 */
    permissions?: string[]
    /** 面包屑路径 */
    breadcrumb?: Array<{ name: string, href: string }>
  }
}

export {}
