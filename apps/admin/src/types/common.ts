/**
 * 通用类型定义
 */

// 分页相关类型
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

export interface PaginationResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 表单选项类型
export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
}

// API 响应基础类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 错误类型
export interface ApiError {
  code: number
  message: string
  details?: any
}

// 用户状态类型
export type UserStatus = 0 | 1

// API Key 状态类型
export type ApiKeyStatus = 'ok' | 'blocked'

// 配额状态类型
export type QuotaStatus = 'active' | 'expired' | 'exhausted' | 'disabled'

// 充值状态类型
export type RechargeStatus = 'completed' | 'pending' | 'failed' | 'cancelled'

// 充值类型
export type RechargeType = 'admin' | 'alipay' | 'wechat' | 'bank'

// 配额类型
export type QuotaType = 'admin' | 'purchase' | 'promotion' | 'gift'

// 服务模块类型
export type ServiceModule = 'llm' | 'tts' | 'asr'

// 计费类型
export type BillingType = 'quota' | 'balance'

// 货币类型
export type Currency = 'CNY' | 'USD'

// 时间段类型
export type TimePeriod = 'day' | 'week' | 'month'

// Badge 变体类型
export type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline'

// 表单字段类型
export interface FormField {
  key: string
  label: string
  type: 'text' | 'number' | 'email' | 'password' | 'textarea' | 'select' | 'combobox' | 'switch' | 'date' | 'datetime-local'
  required?: boolean
  placeholder?: string
  defaultValue?: any
  options?: SelectOption[]
  disabled?: boolean
  show?: boolean
  validation?: {
    min?: number
    max?: number
    pattern?: RegExp
    custom?: (value: any) => boolean | string
  }
}

// 表格列配置类型
export interface TableColumn {
  key: string
  title: string
  width?: number
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: any) => any
}

// 统计数据类型
export interface StatsData {
  label: string
  value: number | string
  change?: number
  changeType?: 'increase' | 'decrease' | 'neutral'
  icon?: any
  color?: string
}

// 图表数据类型
export interface ChartDataPoint {
  name: string
  value: number
  color?: string
}

// 文件上传类型
export interface UploadFile {
  id: string
  name: string
  size: number
  type: string
  url?: string
  status: 'uploading' | 'success' | 'error'
  progress?: number
}

// 通知类型
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  actions?: Array<{
    label: string
    action: () => void
  }>
}

// 菜单项类型
export interface MenuItem {
  id: string
  label: string
  icon?: any
  path?: string
  children?: MenuItem[]
  disabled?: boolean
  badge?: string | number
}

// 面包屑类型
export interface BreadcrumbItem {
  label: string
  path?: string
  icon?: any
}

// 搜索过滤器类型
export interface SearchFilter {
  key: string
  label: string
  type: 'text' | 'select' | 'date' | 'daterange'
  options?: SelectOption[]
  defaultValue?: any
}

// 操作按钮类型
export interface ActionButton {
  label: string
  icon?: any
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  onClick: () => void | Promise<void>
}

// 模态框配置类型
export interface ModalConfig {
  title: string
  content?: string
  confirmText?: string
  cancelText?: string
  type?: 'info' | 'warning' | 'error' | 'success'
  onConfirm?: () => void | Promise<void>
  onCancel?: () => void
}

// 主题配置类型
export interface ThemeConfig {
  primaryColor: string
  darkMode: boolean
  fontSize: 'small' | 'medium' | 'large'
  borderRadius: 'none' | 'small' | 'medium' | 'large'
}

// 用户偏好设置类型
export interface UserPreferences {
  theme: ThemeConfig
  language: string
  timezone: string
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
}

// 权限类型
export interface Permission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
}

// 角色类型
export interface Role {
  id: string
  name: string
  description?: string
  permissions: Permission[]
}

// 审计日志类型
export interface AuditLog {
  id: string
  userId: string
  userName: string
  action: string
  resource: string
  resourceId?: string
  details?: any
  ip: string
  userAgent: string
  createdAt: string
}
