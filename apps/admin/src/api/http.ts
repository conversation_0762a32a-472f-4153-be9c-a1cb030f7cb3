import { useAxios } from '@uozi-admin/request'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { PATH_LOGIN } from '@/router/auth'
import { useUserStore } from '@/store/user'

export interface ModelBase {
  id: string
  created_at: string
  updated_at: string
}

const { setRequestInterceptor, setResponseInterceptor } = useAxios()

const router = useRouter()

export function serviceInterceptor() {
  setRequestInterceptor((config) => {
    const user = useUserStore()

    const { token } = storeToRefs(user)
    if (token)
      config.headers.Token = token.value

    return config
  })

  setResponseInterceptor(
    (response) => {
      return Promise.resolve(response.data)
    },
    async (error) => {
      const user = useUserStore()

      switch (error.response.status) {
        case 401:
        case 403:
          user.reset()
          await router.push(PATH_LOGIN)
          break
      }

      // Handle JSON error that comes back as Blob for blob request type
      if (error?.response?.data instanceof Blob && error?.response?.data?.type === 'application/json') {
        try {
          const text = await error.response.data.text()
          error.response.data = JSON.parse(text)
        }
        catch (e) {
          // If parsing fails, we'll continue with the original error.response.data

          console.error('Failed to parse blob error response as JSON', e)
        }
      }

      return Promise.reject(error.response?.data)
    },
  )
}
