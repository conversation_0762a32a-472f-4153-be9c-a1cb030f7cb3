import { http } from '@uozi-admin/request'

// 上传结果接口
export interface UploadResult {
  url: string
  size: number
  mime: string
  filename: string
}

// 上传文件接口
export interface UploadFile {
  id: string
  user_id: string
  mime: string
  name: string
  path: string
  thumbnail?: string
  size: number
  to: string
  created_at: string
  updated_at: string
}

export const uploadApi = {
  // 上传文件（用于头像等）
  upload: async (file: File, to: string = ''): Promise<UploadFile> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('to', to)

    return await http.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 上传临时文件
  uploadTemp: async (file: File): Promise<UploadResult> => {
    const formData = new FormData()
    formData.append('file', file)

    return await http.post('/upload_temp', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  // 获取上传文件URL
  getUploadUrl: async (path: string): Promise<{ url: string }> => {
    return await http.get('/upload/url', {
      params: { path },
    })
  },
}
