import { extendCurdApi, http, useCurdApi } from '@uozi-admin/request'

// 充值记录接口
export interface RechargeRecord {
  id: string
  user_id: string
  user?: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  amount: number
  type: string
  status: string
  trade_no?: string
  description?: string
  operator_id?: string
  operator?: {
    id: string
    name: string
    email: string
  }
  created_at: string
  updated_at: string
}

// 创建充值请求接口
export interface CreateRechargeRequest {
  user_id: string
  amount: number
  type?: string
  description?: string
}

// 充值统计响应接口
export interface RechargeStatsResponse {
  today_amount: number
  month_amount: number
  total_count: number
  average_amount: number
  pending_amount: number
  completed_amount: number
  failed_amount: number
  total_users: number
  active_users: number
  total_balance: number
}

// 用户余额信息接口
export interface UserBalanceInfo {
  id: string
  name: string
  email: string
  balance: number
  last_recharge: string
}

export const rechargeApi = extendCurdApi(useCurdApi<RechargeRecord>('/admin/billing/recharge_records'), {
  recharge: async (data: CreateRechargeRequest) => {
    return await http.post('/admin/billing/recharge', data)
  },
  getStats: async (): Promise<RechargeStatsResponse> => {
    return await http.get('/admin/billing/recharge/stats')
  },
  getUserBalances: async (): Promise<UserBalanceInfo[]> => {
    return await http.get('/admin/billing/recharge/user-balances')
  },
})
