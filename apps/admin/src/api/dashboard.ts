import { http } from '@uozi-admin/request'

// Dashboard统计数据接口
export interface DashboardStats {
  // 核心指标
  total_revenue: number // 总收入
  month_revenue: number // 本月收入
  today_revenue: number // 今日收入
  total_users: number // 总用户数
  active_users: number // 活跃用户数
  total_keys: number // 总Key数
  active_keys: number // 活跃Key数
  blocked_keys: number // 阻止Key数

  // 计费相关
  total_quota_packages: number // 总资源包数
  active_quota_packages: number // 活跃资源包数
  total_balance: number // 用户总余额
  avg_user_balance: number // 平均用户余额

  // 使用量统计
  total_usage: number // 总使用量
  month_usage: number // 本月使用量
  today_usage: number // 今日使用量
  avg_daily_usage: number // 平均日使用量

  // 趋势数据
  revenue_growth: number // 收入增长率
  user_growth: number // 用户增长率
  usage_growth: number // 使用量增长率
  balance_growth: number // 余额增长率

  // 模块统计
  module_stats: ModuleStatsItem[]
}

// 模块统计项
export interface ModuleStatsItem {
  module: string // 模块名称
  name: string // 显示名称
  usage: number // 使用量
  revenue: number // 收入
  keys: number // Key数量
  growth: number // 增长率
  color: string // 颜色
}

// 近期活跃用户
export interface RecentActiveUser {
  id: string
  name: string
  email: string
  balance: number
  usage: number
  last_active: string
}

// 系统健康状态
export interface SystemHealthStatus {
  api_response_time: number // API响应时间(ms)
  error_rate: number // 错误率
  uptime: number // 系统可用性
  queue_depth: number // 队列深度
  active_connections: number // 活跃连接数
  last_check: string // 最后检查时间
}

// 近期充值记录
export interface RecentRecharge {
  id: string
  user: string
  amount: number
  type: string
  time: string
}

// Dashboard完整数据
export interface DashboardData {
  stats: DashboardStats
  recent_active_users: RecentActiveUser[]
  system_health: SystemHealthStatus
  recent_recharges: RecentRecharge[]
}

// Dashboard API
export const dashboardApi = {
  // 获取Dashboard统计数据
  getStats: async (): Promise<DashboardStats> => {
    return await http.get('/admin/dashboard/stats')
  },

  // 获取Dashboard完整数据
  getData: async (): Promise<DashboardData> => {
    return await http.get('/admin/dashboard/data')
  },
}
