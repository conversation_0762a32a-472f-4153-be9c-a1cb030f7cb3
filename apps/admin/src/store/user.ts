import type { User } from '@/api'
import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useUserStore = defineStore('user', () => {
  const token = ref('')
  const user = ref<User | null>(null)

  // 从localStorage恢复token
  const savedToken = localStorage.getItem('auth_token')
  if (savedToken) {
    token.value = savedToken
  }

  // 监听token变化，自动保存到localStorage
  watch(token, (newToken) => {
    if (newToken) {
      localStorage.setItem('auth_token', newToken)
    }
    else {
      localStorage.removeItem('auth_token')
    }
  })

  const setToken = (t: string) => {
    token.value = t
  }

  const setUser = (u: User) => {
    user.value = u
  }

  const reset = () => {
    token.value = ''
    user.value = null
    localStorage.removeItem('auth_token')
  }

  // 检查是否已登录
  const isLoggedIn = () => {
    return !!token.value
  }

  return {
    token,
    user,
    setToken,
    setUser,
    reset,
    isLoggedIn,
  }
})
