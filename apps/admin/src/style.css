/* 导入基础样式和主题定义 */
@import "@billing/ui/globals.css";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --warning: oklch(0.734 0.194 83.21);  
  --warning-foreground: oklch(0.145 0 0);
  --success: oklch(0.699 0.195 142.95);
  --success-foreground: oklch(0.985 0 0);
  --info: oklch(0.557 0.229 260.116);
  --info-foreground: oklch(0.985 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.55 0.229 25.723);
  --destructive-foreground: oklch(0.98 0.141 25.723);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
  --warning: oklch(0.734 0.194 83.21);
  --warning-foreground: oklch(0.145 0 0);
  --success: oklch(0.699 0.195 142.95);
  --success-foreground: oklch(0.145 0 0);
  --info: oklch(0.665 0.178 257.5);
  --info-foreground: oklch(0.145 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  button {
    @apply cursor-pointer;
  }

  [data-slot="select-item"] {
    @apply cursor-pointer!;
  }

  /* 确保图片在暗色模式下的适配 */
  .dark img {
    filter: brightness(0.8) contrast(1.2);
  }

  .dark img.no-filter {
    filter: none;
  }
}

@layer components {
  /* 增强的渐变背景适配 */
  .gradient-bg-light {
    background: linear-gradient(135deg,
      oklch(0.99 0.005 0) 0%,
      oklch(0.97 0.01 240) 25%,
      oklch(0.98 0.008 180) 50%,
      oklch(0.96 0.012 120) 75%,
      oklch(0.98 0.006 60) 100%);
    position: relative;
  }

  .dark .gradient-bg-light {
    background: linear-gradient(135deg,
      oklch(0.12 0.01 240) 0%,
      oklch(0.14 0.015 200) 25%,
      oklch(0.13 0.012 160) 50%,
      oklch(0.15 0.018 120) 75%,
      oklch(0.13 0.008 80) 100%);
  }

  .gradient-bg-primary {
    @apply bg-gradient-to-br from-primary/10 via-primary/5 to-primary/8;
  }

  .dark .gradient-bg-primary {
    @apply bg-gradient-to-br from-primary/20 via-primary/10 to-primary/15;
  }

  /* 卡片阴影适配 */
  .card-shadow {
    @apply shadow-sm;
  }
  
  .dark .card-shadow {
    @apply shadow-lg shadow-black/10;
  }

  /* 状态指示器 */
  .status-active {
    @apply bg-success text-gray-50;
  }
  
  .status-warning {
    @apply bg-warning text-gray-50;
  }
    
  .status-error {
    @apply bg-destructive text-gray-50;
  }
  
  .status-info {
    @apply bg-info text-gray-50;
  }

  /* 代码块适配 */
  .code-block {
    @apply bg-muted/50 text-foreground border border-border rounded-lg p-4 font-mono text-sm;
  }
  
  .inline-code {
    @apply bg-muted/80 text-foreground px-1.5 py-0.5 rounded text-sm font-mono;
  }

  /* 输入框聚焦状态 */
  .input-focus {
    @apply focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
  }

  /* 过渡动画 */
  .theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }
}

@layer utilities {
  /* 暗色模式特定工具类 */
  .dark-hidden {
    @apply dark:hidden;
  }
  
  .dark-block {
    @apply hidden dark:block;
  }
  
  .light-hidden {
    @apply hidden dark:block;
  }
  
  .light-block {
    @apply block dark:hidden;
  }
}

@source "./**/*.{ts,tsx,vue}";
@source "../../../packages/ui/src/**/*.{ts,tsx,vue}";
@source "../../../packages/curd/src/**/*.{ts,tsx,vue}";