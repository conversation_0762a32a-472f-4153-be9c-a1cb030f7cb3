<script setup lang="ts">
import type { QuotaPackage } from '@/api/quota'
import {
  <PERSON><PERSON>,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from '@billing/ui'
import { reactive, watch } from 'vue'
import { FORM_OPTIONS } from '@/constants'

interface Props {
  open: boolean
  editingQuota: QuotaPackage | null
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'confirm', formData: QuotaFormData): void
}

interface QuotaFormData {
  module: 'llm' | 'tts' | 'asr'
  quota: number
  expires_at: number
  model_name: string
  description: string
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const quotaForm = reactive<QuotaFormData>({
  module: 'llm',
  quota: 1000000,
  expires_at: 0,
  model_name: '',
  description: '',
})

// 监听编辑状态变化，重置表单
watch(() => props.editingQuota, (newQuota) => {
  if (newQuota) {
    // 编辑模式
    quotaForm.module = newQuota.module as 'llm' | 'tts' | 'asr'
    quotaForm.quota = newQuota.quota
    quotaForm.expires_at = newQuota.expires_at || 0
    quotaForm.model_name = newQuota.model_name || ''
    quotaForm.description = newQuota.description || ''
  }
  else {
    // 新增模式
    quotaForm.module = 'llm'
    quotaForm.quota = 1000000
    quotaForm.expires_at = 0
    quotaForm.model_name = ''
    quotaForm.description = ''
  }
})

function handleClose() {
  emit('update:open', false)
}

function handleConfirm() {
  emit('confirm', { ...quotaForm })
}
</script>

<template>
  <Dialog
    :open="open"
    @update:open="handleClose"
  >
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>{{ editingQuota ? '编辑配额' : '添加配额' }}</DialogTitle>
      </DialogHeader>
      <div class="space-y-4">
        <div>
          <Label>模块</Label>
          <Select
            v-model="quotaForm.module"
            :disabled="!!editingQuota"
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="option in FORM_OPTIONS.SERVICE_MODULES"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </SelectItem>
            </SelectContent>
          </Select>
          <div
            v-if="editingQuota"
            class="text-xs text-gray-500 mt-1"
          >
            编辑模式下不能修改模块类型
          </div>
        </div>

        <div>
          <Label>配额</Label>
          <Input
            v-model.number="quotaForm.quota"
            type="number"
            min="0"
            placeholder="输入配额数量"
          />
        </div>

        <div>
          <Label>模型名称（可选）</Label>
          <Input
            v-model="quotaForm.model_name"
            :disabled="!!editingQuota"
            placeholder="留空表示通用模型"
          />
          <div
            v-if="editingQuota"
            class="text-xs text-gray-500 mt-1"
          >
            编辑模式下不能修改模型名称
          </div>
        </div>

        <div>
          <Label>过期时间</Label>
          <Input
            v-model="quotaForm.expires_at"
            type="datetime-local"
            placeholder="选择过期时间"
          />
        </div>

        <div>
          <Label>描述</Label>
          <Textarea
            v-model="quotaForm.description"
            placeholder="输入配额描述"
          />
        </div>

        <div class="flex justify-end gap-2">
          <Button
            variant="outline"
            @click="handleClose"
          >
            取消
          </Button>
          <Button @click="handleConfirm">
            {{ editingQuota ? '保存' : '添加' }}
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
