<script setup lang="ts">
import { Button } from '@billing/ui'
import { ArrowLeft, Home, Search } from 'lucide-vue-next'
import { useRouter } from 'vue-router'

const router = useRouter()

// 返回上一页
function goBack() {
  router.go(-1)
}

// 回到首页
function goHome() {
  router.push('/')
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
      <!-- 404图标 -->
      <div class="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-gray-100">
        <Search class="h-16 w-16 text-gray-400" />
      </div>

      <!-- 错误信息 -->
      <div class="space-y-4">
        <h1 class="text-9xl font-bold text-gray-300">
          404
        </h1>
        <h2 class="text-2xl font-semibold text-gray-900">
          页面未找到
        </h2>
        <p class="text-gray-600">
          抱歉，您访问的页面不存在或已被移除。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <Button
          variant="outline"
          @click="goBack"
        >
          <ArrowLeft class="w-4 h-4 mr-2" />
          返回上一页
        </Button>
        <Button @click="goHome">
          <Home class="w-4 h-4 mr-2" />
          回到首页
        </Button>
      </div>

      <!-- 帮助链接 -->
      <div class="text-sm text-gray-500 space-y-2">
        <p>如果您需要帮助，可以：</p>
        <div class="flex flex-col space-y-1">
          <RouterLink
            to="/users"
            class="text-blue-600 hover:text-blue-800"
          >
            前往用户管理
          </RouterLink>
          <RouterLink
            to="/billing/keys"
            class="text-blue-600 hover:text-blue-800"
          >
            前往API Key管理
          </RouterLink>
          <RouterLink
            to="/settings"
            class="text-blue-600 hover:text-blue-800"
          >
            前往系统设置
          </RouterLink>
        </div>
      </div>
    </div>
  </div>
</template>
