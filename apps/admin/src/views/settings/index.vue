<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@billing/ui'
import { ChevronRight, Lock, Settings as SettingsIcon, Shield } from 'lucide-vue-next'

// 设置分类配置
const settingsCategories = [
  {
    id: 'general',
    title: '通用设置',
    description: '配置系统的基本信息、功能开关和默认配额等',
    icon: SettingsIcon,
    path: '/settings/general',
    items: [
      '基本信息配置',
      '功能开关管理',
      '地区与格式设置',
      '默认配额配置',
      '文件上传设置',
    ],
  },
  {
    id: 'auth',
    title: '认证设置',
    description: '配置登录安全、会话管理和密码策略等',
    icon: Shield,
    path: '/settings/auth',
    items: [
      '登录失败次数限制',
      'IP阻止策略',
      '会话超时设置',
      '双因素认证',
      '密码过期策略',
    ],
  },
  {
    id: 'security',
    title: '安全设置',
    description: '高级安全配置和审计日志设置（开发中）',
    icon: Lock,
    path: '/settings/security',
    disabled: true,
    items: [
      'API访问控制',
      '审计日志配置',
      '数据加密设置',
      '备份策略',
    ],
  },
]
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-semibold text-gray-900">
        系统设置
      </h1>
      <p class="mt-2 text-sm text-gray-700">
        配置和管理系统的各项参数设置。
      </p>
    </div>

    <!-- 设置分类卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="category in settingsCategories"
        :key="category.id"
      >
        <Card
          class="h-full transition-all duration-200 hover:shadow-md"
          :class="[
            category.disabled
              ? 'opacity-60 cursor-not-allowed'
              : 'cursor-pointer hover:border-gray-300',
          ]"
          @click="category.disabled ? null : $router.push(category.path)"
        >
          <CardHeader class="pb-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div
                  class="flex items-center justify-center w-10 h-10 rounded-lg"
                  :class="[
                    category.disabled
                      ? 'bg-gray-100'
                      : 'bg-blue-50',
                  ]"
                >
                  <component
                    :is="category.icon"
                    class="w-5 h-5"
                    :class="[
                      category.disabled
                        ? 'text-gray-400'
                        : 'text-blue-600',
                    ]"
                  />
                </div>
                <div>
                  <CardTitle class="text-lg">
                    {{ category.title }}
                  </CardTitle>
                  <div
                    v-if="category.disabled"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mt-1"
                  >
                    开发中
                  </div>
                </div>
              </div>
              <ChevronRight
                v-if="!category.disabled"
                class="w-5 h-5 text-gray-400"
              />
            </div>
            <CardDescription class="mt-2">
              {{ category.description }}
            </CardDescription>
          </CardHeader>

          <CardContent class="pt-0">
            <div class="space-y-2">
              <div class="text-sm font-medium text-gray-900 mb-3">
                主要功能：
              </div>
              <ul class="space-y-2">
                <li
                  v-for="item in category.items"
                  :key="item"
                  class="flex items-center text-sm text-gray-600"
                >
                  <div class="w-1.5 h-1.5 bg-gray-400 rounded-full mr-3 flex-shrink-0" />
                  {{ item }}
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- 快速访问链接 -->
    <Card>
      <CardHeader>
        <CardTitle>快速访问</CardTitle>
        <CardDescription>
          常用的系统配置项目
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <RouterLink
            to="/settings/general"
            class="flex items-center p-3 border rounded-lg hover:bg-gray-50 transition-colors group"
          >
            <SettingsIcon class="w-5 h-5 text-gray-400 group-hover:text-gray-600" />
            <div class="ml-3">
              <div class="text-sm font-medium">
                默认配额设置
              </div>
              <div class="text-xs text-gray-500">
                配置新用户默认配额
              </div>
            </div>
          </RouterLink>

          <RouterLink
            to="/settings/auth"
            class="flex items-center p-3 border rounded-lg hover:bg-gray-50 transition-colors group"
          >
            <Shield class="w-5 h-5 text-gray-400 group-hover:text-gray-600" />
            <div class="ml-3">
              <div class="text-sm font-medium">
                登录安全
              </div>
              <div class="text-xs text-gray-500">
                配置登录失败次数限制
              </div>
            </div>
          </RouterLink>

          <RouterLink
            to="/settings/general"
            class="flex items-center p-3 border rounded-lg hover:bg-gray-50 transition-colors group"
          >
            <SettingsIcon class="w-5 h-5 text-gray-400 group-hover:text-gray-600" />
            <div class="ml-3">
              <div class="text-sm font-medium">
                功能开关
              </div>
              <div class="text-xs text-gray-500">
                管理系统功能启用状态
              </div>
            </div>
          </RouterLink>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
