<script setup lang="ts">
import { Button, Card, CardContent, CardHeader, CardTitle, Input, Label, Switch, Textarea } from '@billing/ui'
import { AlertCircle, CheckCircle, RefreshCw, Save } from 'lucide-vue-next'
import { reactive, ref } from 'vue'

// 通用设置表单
const generalSettings = reactive({
  siteName: '计费系统管理端',
  siteDescription: '现代化的API计费管理系统',
  contactEmail: '<EMAIL>',
  enableRegistration: true,
  enableEmailNotification: true,
  enableSmsNotification: false,
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD',
  currency: 'CNY',
  defaultQuotaLlm: 1000000,
  defaultQuotaTts: 100000,
  defaultQuotaAsr: 100000,
  maxFileSize: 10, // MB
  allowedFileTypes: 'jpg,png,pdf,doc,docx',
})

const saving = ref(false)
const loading = ref(false)
const message = ref('')
const messageType = ref<'success' | 'error' | ''>('')

// 显示消息
function showMessage(text: string, type: 'success' | 'error') {
  message.value = text
  messageType.value = type
  setTimeout(() => {
    message.value = ''
    messageType.value = ''
  }, 3000)
}

// 保存设置
async function saveSettings() {
  saving.value = true
  try {
    // TODO: 调用保存设置的API
    // await settingsApi.updateGeneralSettings(generalSettings)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    console.log('通用设置已保存:', generalSettings)
    showMessage('设置保存成功！', 'success')
  }
  catch (error) {
    console.error('保存设置失败:', error)
    showMessage('保存失败，请重试', 'error')
  }
  finally {
    saving.value = false
  }
}

// 重置设置
async function resetSettings() {
  loading.value = true
  try {
    // TODO: 调用重置设置的API
    // await settingsApi.resetGeneralSettings()

    // 模拟重置
    Object.assign(generalSettings, {
      siteName: '计费系统管理端',
      siteDescription: '现代化的API计费管理系统',
      contactEmail: '<EMAIL>',
      enableRegistration: true,
      enableEmailNotification: true,
      enableSmsNotification: false,
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      currency: 'CNY',
      defaultQuotaLlm: 1000000,
      defaultQuotaTts: 100000,
      defaultQuotaAsr: 100000,
      maxFileSize: 10,
      allowedFileTypes: 'jpg,png,pdf,doc,docx',
    })

    showMessage('设置已重置为默认值', 'success')
  }
  catch (error) {
    console.error('重置设置失败:', error)
    showMessage('重置失败，请重试', 'error')
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          通用设置
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          配置系统的通用参数和动态设置项。
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          :disabled="loading"
          @click="resetSettings"
        >
          <RefreshCw class="w-4 h-4 mr-2" />
          重置默认
        </Button>
        <Button
          :disabled="saving"
          @click="saveSettings"
        >
          <Save class="w-4 h-4 mr-2" />
          {{ saving ? '保存中...' : '保存设置' }}
        </Button>
      </div>
    </div>

    <!-- 消息显示区域 -->
    <div
      v-if="message"
      class="rounded-md p-4"
      :class="messageType === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'"
    >
      <div class="flex">
        <component
          :is="messageType === 'success' ? CheckCircle : AlertCircle"
          class="h-5 w-5"
          :class="messageType === 'success' ? 'text-green-400' : 'text-red-400'"
        />
        <div class="ml-3">
          <p
            class="text-sm"
            :class="messageType === 'success' ? 'text-green-800' : 'text-red-800'"
          >
            {{ message }}
          </p>
        </div>
      </div>
    </div>

    <!-- 基本信息设置 -->
    <Card>
      <CardHeader>
        <CardTitle>基本信息</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <Label for="siteName">站点名称</Label>
            <Input
              id="siteName"
              v-model="generalSettings.siteName"
              placeholder="输入站点名称"
            />
          </div>

          <div class="space-y-2">
            <Label for="contactEmail">联系邮箱</Label>
            <Input
              id="contactEmail"
              v-model="generalSettings.contactEmail"
              type="email"
              placeholder="输入联系邮箱"
            />
          </div>
        </div>

        <div class="space-y-2">
          <Label for="siteDescription">站点描述</Label>
          <Textarea
            id="siteDescription"
            v-model="generalSettings.siteDescription"
            placeholder="输入站点描述"
            rows="3"
          />
        </div>
      </CardContent>
    </Card>

    <!-- 功能开关 -->
    <Card>
      <CardHeader>
        <CardTitle>功能开关</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <Label>允许用户注册</Label>
            <p class="text-sm text-gray-500">
              开放用户自主注册功能
            </p>
          </div>
          <Switch v-model="generalSettings.enableRegistration" />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <Label>启用邮件通知</Label>
            <p class="text-sm text-gray-500">
              系统事件邮件通知功能
            </p>
          </div>
          <Switch v-model="generalSettings.enableEmailNotification" />
        </div>

        <div class="flex items-center justify-between">
          <div>
            <Label>启用短信通知</Label>
            <p class="text-sm text-gray-500">
              系统事件短信通知功能
            </p>
          </div>
          <Switch v-model="generalSettings.enableSmsNotification" />
        </div>
      </CardContent>
    </Card>

    <!-- 地区与格式 -->
    <Card>
      <CardHeader>
        <CardTitle>地区与格式</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-2">
            <Label for="timezone">时区</Label>
            <Input
              id="timezone"
              v-model="generalSettings.timezone"
              placeholder="Asia/Shanghai"
            />
          </div>

          <div class="space-y-2">
            <Label for="dateFormat">日期格式</Label>
            <Input
              id="dateFormat"
              v-model="generalSettings.dateFormat"
              placeholder="YYYY-MM-DD"
            />
          </div>

          <div class="space-y-2">
            <Label for="currency">货币单位</Label>
            <Input
              id="currency"
              v-model="generalSettings.currency"
              placeholder="CNY"
            />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 默认配额设置 -->
    <Card>
      <CardHeader>
        <CardTitle>默认配额设置</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="space-y-2">
            <Label for="defaultQuotaLlm">默认LLM配额</Label>
            <Input
              id="defaultQuotaLlm"
              v-model.number="generalSettings.defaultQuotaLlm"
              type="number"
              placeholder="1000000"
            />
          </div>

          <div class="space-y-2">
            <Label for="defaultQuotaTts">默认TTS配额</Label>
            <Input
              id="defaultQuotaTts"
              v-model.number="generalSettings.defaultQuotaTts"
              type="number"
              placeholder="100000"
            />
          </div>

          <div class="space-y-2">
            <Label for="defaultQuotaAsr">默认ASR配额</Label>
            <Input
              id="defaultQuotaAsr"
              v-model.number="generalSettings.defaultQuotaAsr"
              type="number"
              placeholder="100000"
            />
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 文件上传设置 -->
    <Card>
      <CardHeader>
        <CardTitle>文件上传设置</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <Label for="maxFileSize">最大文件大小 (MB)</Label>
            <Input
              id="maxFileSize"
              v-model.number="generalSettings.maxFileSize"
              type="number"
              min="1"
              max="100"
              placeholder="10"
            />
          </div>

          <div class="space-y-2">
            <Label for="allowedFileTypes">允许的文件类型</Label>
            <Input
              id="allowedFileTypes"
              v-model="generalSettings.allowedFileTypes"
              placeholder="jpg,png,pdf,doc,docx"
            />
            <p class="text-xs text-gray-500">
              用逗号分隔多个文件扩展名
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
