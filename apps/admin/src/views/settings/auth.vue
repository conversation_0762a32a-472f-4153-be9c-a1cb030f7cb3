<script setup lang="ts">
import { Button, Card, CardContent, CardHeader, CardTitle, Input, Label, Switch } from '@billing/ui'
import { AlertCircle, CheckCircle, RefreshCw, Save } from 'lucide-vue-next'
import { reactive, ref } from 'vue'

// 认证设置表单
const authSettings = reactive({
  maxLoginAttempts: 5,
  lockoutDuration: 30, // 分钟
  enableIpBlocking: true,
  ipBlockDuration: 60, // 分钟
  enableTwoFactor: false,
  sessionTimeout: 120, // 分钟
  enablePasswordExpiry: false,
  passwordExpiryDays: 90,
})

const saving = ref(false)
const loading = ref(false)
const message = ref('')
const messageType = ref<'success' | 'error' | ''>('')

// 显示消息
function showMessage(text: string, type: 'success' | 'error') {
  message.value = text
  messageType.value = type
  setTimeout(() => {
    message.value = ''
    messageType.value = ''
  }, 3000)
}

// 保存设置
async function saveSettings() {
  saving.value = true
  try {
    // TODO: 调用保存设置的API
    // await settingsApi.updateAuthSettings(authSettings)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    console.log('认证设置已保存:', authSettings)
    showMessage('设置保存成功！', 'success')
  }
  catch (error) {
    console.error('保存设置失败:', error)
    showMessage('保存失败，请重试', 'error')
  }
  finally {
    saving.value = false
  }
}

// 重置设置
async function resetSettings() {
  loading.value = true
  try {
    // TODO: 调用重置设置的API
    // await settingsApi.resetAuthSettings()

    // 模拟重置
    Object.assign(authSettings, {
      maxLoginAttempts: 5,
      lockoutDuration: 30,
      enableIpBlocking: true,
      ipBlockDuration: 60,
      enableTwoFactor: false,
      sessionTimeout: 120,
      enablePasswordExpiry: false,
      passwordExpiryDays: 90,
    })

    showMessage('设置已重置为默认值', 'success')
  }
  catch (error) {
    console.error('重置设置失败:', error)
    showMessage('重置失败，请重试', 'error')
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          认证设置
        </h1>
        <p class="mt-2 text-sm text-gray-700">
          配置登录失败次数限制、IP禁用等安全设置。
        </p>
      </div>
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          :disabled="loading"
          @click="resetSettings"
        >
          <RefreshCw class="w-4 h-4 mr-2" />
          重置默认
        </Button>
        <Button
          :disabled="saving"
          @click="saveSettings"
        >
          <Save class="w-4 h-4 mr-2" />
          {{ saving ? '保存中...' : '保存设置' }}
        </Button>
      </div>
    </div>

    <!-- 消息显示区域 -->
    <div
      v-if="message"
      class="rounded-md p-4"
      :class="messageType === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'"
    >
      <div class="flex">
        <component
          :is="messageType === 'success' ? CheckCircle : AlertCircle"
          class="h-5 w-5"
          :class="messageType === 'success' ? 'text-green-400' : 'text-red-400'"
        />
        <div class="ml-3">
          <p
            class="text-sm"
            :class="messageType === 'success' ? 'text-green-800' : 'text-red-800'"
          >
            {{ message }}
          </p>
        </div>
      </div>
    </div>

    <!-- 登录安全设置 -->
    <Card>
      <CardHeader>
        <CardTitle>登录安全</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <Label for="maxLoginAttempts">最大登录尝试次数</Label>
            <Input
              id="maxLoginAttempts"
              v-model.number="authSettings.maxLoginAttempts"
              type="number"
              min="1"
              max="10"
              placeholder="输入最大尝试次数"
            />
            <p class="text-xs text-gray-500">
              超过此次数将锁定账户
            </p>
          </div>

          <div class="space-y-2">
            <Label for="lockoutDuration">账户锁定时长 (分钟)</Label>
            <Input
              id="lockoutDuration"
              v-model.number="authSettings.lockoutDuration"
              type="number"
              min="5"
              max="1440"
              placeholder="输入锁定时长"
            />
            <p class="text-xs text-gray-500">
              账户被锁定后的解锁时间
            </p>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <Label>启用IP阻止</Label>
            <p class="text-sm text-gray-500">
              对多次失败的IP地址进行临时阻止
            </p>
          </div>
          <Switch v-model="authSettings.enableIpBlocking" />
        </div>

        <div
          v-if="authSettings.enableIpBlocking"
          class="space-y-2"
        >
          <Label for="ipBlockDuration">IP阻止时长 (分钟)</Label>
          <Input
            id="ipBlockDuration"
            v-model.number="authSettings.ipBlockDuration"
            type="number"
            min="10"
            max="1440"
            class="w-48"
            placeholder="输入阻止时长"
          />
        </div>
      </CardContent>
    </Card>

    <!-- 会话设置 -->
    <Card>
      <CardHeader>
        <CardTitle>会话设置</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="space-y-2">
          <Label for="sessionTimeout">会话超时时间 (分钟)</Label>
          <Input
            id="sessionTimeout"
            v-model.number="authSettings.sessionTimeout"
            type="number"
            min="30"
            max="480"
            class="w-48"
            placeholder="输入超时时间"
          />
          <p class="text-xs text-gray-500">
            用户无操作后自动登出的时间
          </p>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <Label>启用双因素认证</Label>
            <p class="text-sm text-gray-500">
              要求用户使用双因素认证登录
            </p>
          </div>
          <Switch v-model="authSettings.enableTwoFactor" />
        </div>
      </CardContent>
    </Card>

    <!-- 密码策略 -->
    <Card>
      <CardHeader>
        <CardTitle>密码策略</CardTitle>
      </CardHeader>
      <CardContent class="space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <Label>启用密码过期</Label>
            <p class="text-sm text-gray-500">
              强制用户定期更换密码
            </p>
          </div>
          <Switch v-model="authSettings.enablePasswordExpiry" />
        </div>

        <div
          v-if="authSettings.enablePasswordExpiry"
          class="space-y-2"
        >
          <Label for="passwordExpiryDays">密码有效期 (天)</Label>
          <Input
            id="passwordExpiryDays"
            v-model.number="authSettings.passwordExpiryDays"
            type="number"
            min="30"
            max="365"
            class="w-48"
            placeholder="输入有效期天数"
          />
        </div>
      </CardContent>
    </Card>
  </div>
</template>
