<script setup lang="ts">
import { <PERSON><PERSON>, Card, CardContent, CardHeader, Checkbox, Input, Label } from '@billing/ui'
import { AlertCircle, Eye, EyeOff, Loader2, Lock, Mail, Shield } from 'lucide-vue-next'
import { computed, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { authApi } from '@/api/auth'
import { useUserStore } from '@/store/user'

const router = useRouter()
const userStore = useUserStore()

const form = reactive({
  email: '',
  password: '',
  remember: false,
})

const loading = ref(false)
const error = ref('')
const showPassword = ref(false)
const emailFocused = ref(false)
const passwordFocused = ref(false)

// 表单验证状态
const isFormValid = computed(() => {
  return form.email.trim() !== '' && form.password.trim() !== '' && form.email.includes('@')
})

const emailError = computed(() => {
  if (!form.email)
    return ''
  if (!form.email.includes('@'))
    return '请输入有效的邮箱地址'
  return ''
})

function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}

async function handleLogin() {
  if (!isFormValid.value)
    return

  loading.value = true
  error.value = ''

  try {
    const response = await authApi.login({
      email: form.email,
      password: form.password,
    })

    // 假设返回的数据包含token和user信息
    if (response.token) {
      userStore.setToken(response.token)

      // 如果选择记住登录状态，保存到localStorage
      if (form.remember) {
        localStorage.setItem('auth_token', response.token)
      }

      // 跳转到主页
      await router.push('/dashboard')
    }
  }
  catch (err: any) {
    console.error('登录失败:', err)
    error.value = err.message || '登录失败，请检查邮箱和密码'
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center gradient-bg-light py-8 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
    <!-- 增强的背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- 主要装饰圆形 -->
      <div class="bg-decoration absolute -top-32 -right-32 w-96 h-96 bg-gradient-to-br from-primary/8 via-primary/4 to-transparent rounded-full blur-3xl" />
      <div class="bg-decoration absolute -bottom-32 -left-32 w-96 h-96 bg-gradient-to-tr from-primary/6 via-primary/3 to-transparent rounded-full blur-3xl" />

      <!-- 次要装饰元素 -->
      <div class="bg-decoration absolute top-1/4 -left-20 w-40 h-40 bg-primary/3 rounded-full blur-2xl" />
      <div class="bg-decoration absolute bottom-1/4 -right-20 w-32 h-32 bg-primary/4 rounded-full blur-2xl" />

      <!-- 微妙的网格纹理 -->
      <div class="absolute inset-0 opacity-[0.02] bg-[radial-gradient(circle_at_1px_1px,_rgba(0,0,0,0.15)_1px,_transparent_0)] bg-[length:20px_20px]" />
    </div>

    <div class="relative w-full max-w-md">
      <Card class="card-entrance card-shadow card-hover enhanced-blur subtle-texture gpu-accelerated border-0">
        <CardHeader class="text-center pb-6">
          <!-- Logo/图标 -->
          <div class="logo-container gpu-accelerated mx-auto w-16 h-16 bg-gradient-to-br from-primary/15 to-primary/5 rounded-2xl flex items-center justify-center mb-4 transition-all duration-300 hover:scale-105 shadow-lg shadow-primary/10">
            <Shield class="w-8 h-8 text-primary transition-transform duration-300" />
          </div>

          <h1 class="text-2xl font-bold text-foreground mb-2">
            欢迎回来
          </h1>
          <p class="text-muted-foreground text-sm">
            登录计费系统管理端
          </p>
        </CardHeader>

        <CardContent class="space-y-6">
          <form
            class="space-y-4"
            @submit.prevent="handleLogin"
          >
            <!-- 邮箱输入框 -->
            <div class="space-y-2">
              <Label
                for="email"
                class="text-sm font-medium text-foreground"
              >
                邮箱地址
              </Label>
              <div class="relative group focus-scale input-glow">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail class="h-4 w-4 text-muted-foreground group-focus-within:text-primary transition-colors" />
                </div>
                <Input
                  id="email"
                  v-model="form.email"
                  type="email"
                  autocomplete="email"
                  placeholder="请输入邮箱地址"
                  :disabled="loading"
                  class="pl-10 h-11 transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                  :class="{
                    'input-error': emailError && form.email,
                    'input-success': !emailError && form.email && form.email.includes('@'),
                    'loading-pulse': loading,
                  }"
                  @focus="emailFocused = true"
                  @blur="emailFocused = false"
                />
              </div>
              <div
                v-if="emailError && form.email"
                class="text-xs text-destructive flex items-center gap-1"
              >
                <AlertCircle class="h-3 w-3" />
                {{ emailError }}
              </div>
            </div>

            <!-- 密码输入框 -->
            <div class="space-y-2">
              <Label
                for="password"
                class="text-sm font-medium text-foreground"
              >
                密码
              </Label>
              <div class="relative group focus-scale input-glow">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock class="h-4 w-4 text-muted-foreground group-focus-within:text-primary transition-colors" />
                </div>
                <Input
                  id="password"
                  v-model="form.password"
                  :type="showPassword ? 'text' : 'password'"
                  autocomplete="current-password"
                  placeholder="请输入密码"
                  :disabled="loading"
                  class="pl-10 pr-10 h-11 transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                  :class="{
                    'loading-pulse': loading,
                  }"
                  @focus="passwordFocused = true"
                  @blur="passwordFocused = false"
                />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors hover:scale-110"
                  :disabled="loading"
                  @click="togglePasswordVisibility"
                >
                  <Eye
                    v-if="!showPassword"
                    class="h-4 w-4"
                  />
                  <EyeOff
                    v-else
                    class="h-4 w-4"
                  />
                </button>
              </div>
            </div>

            <!-- 记住登录 -->
            <div class="flex items-center justify-between pt-2">
              <div class="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  v-model="form.remember"
                  :disabled="loading"
                  class="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />
                <Label
                  for="remember"
                  class="text-sm text-muted-foreground cursor-pointer select-none"
                >
                  记住登录状态
                </Label>
              </div>
            </div>

            <!-- 错误提示 -->
            <div
              v-if="error"
              class="error-shake rounded-lg bg-destructive/10 border border-destructive/20 p-3 animate-in slide-in-from-top-2 duration-300"
            >
              <div class="flex items-start gap-2">
                <AlertCircle class="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                <div class="flex-1">
                  <p class="text-sm font-medium text-destructive">
                    登录失败
                  </p>
                  <p class="text-xs text-destructive/80 mt-1">
                    {{ error }}
                  </p>
                </div>
              </div>
            </div>

            <!-- 登录按钮 -->
            <Button
              type="submit"
              size="lg"
              class="btn-ripple w-full h-11 font-medium transition-all! duration-200 hover:scale-[1.02] active:scale-[0.98]"
              :disabled="loading || !isFormValid"
              :class="{
                'opacity-50 cursor-not-allowed': !isFormValid,
                'bg-primary hover:bg-primary/90': isFormValid,
                'loading-pulse': loading,
              }"
            >
              <Loader2
                v-if="loading"
                class="w-4 h-4 mr-2 animate-spin"
              />
              <span>{{ loading ? '登录中...' : '登录' }}</span>
            </Button>
          </form>
        </CardContent>
      </Card>

      <!-- 底部信息 -->
      <div class="mt-6 text-center">
        <p class="text-xs text-muted-foreground">
          © 2024 计费系统. 保留所有权利.
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 动画偏好设置支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 优化的动画关键帧 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-3px) rotate(0.5deg);
  }
  50% {
    transform: translateY(-5px) rotate(0deg);
  }
  75% {
    transform: translateY(-3px) rotate(-0.5deg);
  }
}

@keyframes gentle-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(var(--primary), 0.15);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(var(--primary), 0.05);
  }
}

@keyframes background-drift {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(10px, -10px) scale(1.05);
  }
  66% {
    transform: translate(-5px, 5px) scale(0.95);
  }
}

@keyframes card-entrance {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Logo 微妙浮动动画 */
.logo-container {
  animation: float 8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  will-change: transform;
}

/* 优化的输入框发光效果 */
.input-glow:focus-within {
  animation: gentle-glow 3s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

/* 改进的按钮波纹效果 */
.btn-ripple {
  position: relative;
  overflow: hidden;
  transform: translateZ(0); /* 硬件加速 */
}

.btn-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 70%, transparent 100%);
  transform: translate(-50%, -50%);
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.btn-ripple:active::before {
  width: 200px;
  height: 200px;
}

/* 背景装饰动画 */
.bg-decoration {
  animation: background-drift 20s ease-in-out infinite;
  will-change: transform;
}

.bg-decoration:nth-child(2) {
  animation-delay: -10s;
  animation-duration: 25s;
}

/* 卡片入场动画 */
.card-entrance {
  animation: card-entrance 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 优化的加载状态脉冲效果 */
.loading-pulse {
  animation: gentle-pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes gentle-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(0.98);
  }
}

/* 改进的错误提示动画 */
.error-shake {
  animation: gentle-shake 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes gentle-shake {
  0%, 100% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(-3px);
  }
  40% {
    transform: translateX(3px);
  }
  60% {
    transform: translateX(-2px);
  }
  80% {
    transform: translateX(2px);
  }
}

/* 表单验证状态过渡 */
.input-success {
  border-color: rgb(34 197 94);
  box-shadow: 0 0 0 3px rgb(34 197 94 / 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-error {
  border-color: rgb(239 68 68);
  box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 优化的过渡效果 */
* {
  transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 聚焦时的微妙缩放效果 */
.focus-scale:focus-within {
  transform: scale(1.01);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 卡片悬浮效果 */
.card-hover {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 暗色模式优化 */
.dark .card-hover:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* 毛玻璃效果优化 */
.enhanced-blur {
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .enhanced-blur {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计优化 */
@media (max-width: 640px) {
  .logo-container {
    animation-duration: 10s; /* 移动端减缓动画 */
  }

  .card-hover:hover {
    transform: translateY(-2px); /* 移动端减少悬浮效果 */
  }

  .focus-scale:focus-within {
    transform: scale(1.005); /* 移动端减少缩放 */
  }
}

/* 高性能优化 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* 微妙的纹理效果 */
.subtle-texture {
  position: relative;
}

.subtle-texture::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
  pointer-events: none;
  border-radius: inherit;
}
</style>
