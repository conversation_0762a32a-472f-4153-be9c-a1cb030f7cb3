<script setup lang="ts">
import type { EasyCurdConfig } from '@billing/curd'
import type { User } from '@/api'
import { EasyCurd } from '@billing/curd'
import { Avatar, AvatarFallback, AvatarImage, Badge, Button } from '@billing/ui'
import { Eye, Phone } from 'lucide-vue-next'
import { useRouter } from 'vue-router'
import { userApi } from '@/api'
import { formatCurrency, formatDate, formatTimeAgo } from '@/utils'
import {
  PAGINATION,
  FORM_OPTIONS,
  SUCCESS_MESSAGES,
  CONFIRM_MESSAGES
} from '@/constants'

const router = useRouter()

// EasyCurd 配置
const config: EasyCurdConfig<User> = {
  api: userApi,

  columns: [
    {
      accessorKey: 'name',
      header: '姓名',
      meta: {
        isFormField: true,
        fieldType: 'text',
        required: true,
        rules: [
          { required: true, message: '姓名是必填项' },
          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间' },
        ],
        placeholder: '请输入用户姓名',
      },
    },
    {
      accessorKey: 'email',
      header: '邮箱',
      meta: {
        isFormField: true,
        fieldType: 'email',
        required: true,
        rules: [
          { required: true, message: '邮箱是必填项' },
        ],
      },
    },
    {
      accessorKey: 'password',
      header: '密码',
      meta: {
        isFormField: true,
        fieldType: 'password',
        required: true,
        rules: [
          { required: true, message: '密码是必填项' },
          { min: 6, message: '密码长度不能小于6位' },
        ],
        hideInTable: true,
      },
    },
    {
      accessorKey: 'phone',
      header: '手机号',
      meta: {
        isFormField: true,
        fieldType: 'text',
        placeholder: '请输入手机号',
      },
    },
    {
      accessorKey: 'status',
      header: '状态',
      meta: {
        isFormField: true,
        fieldType: 'select',
        defaultValue: 1,
        options: FORM_OPTIONS.USER_STATUS,
      },
    },
    {
      accessorKey: 'balance',
      header: '余额',
      meta: {
        fieldType: 'number',
      },
    },
    {
      accessorKey: 'last_active',
      header: '最近活跃',
      meta: { fieldType: 'datetime' },
    },
    {
      accessorKey: 'created_at',
      header: '注册时间',
      meta: { fieldType: 'datetime' },
    },
    {
      accessorKey: 'actions',
      header: '操作',
    },
  ],

  // 基础配置
  primaryKey: 'id',
  pageSize: PAGINATION.DEFAULT_PAGE_SIZE,
  pageSizeOptions: PAGINATION.PAGE_SIZE_OPTIONS,

  // 功能开关
  features: {
    create: true,
    edit: false,
    delete: true,
    batchDelete: true,
    search: true,
  },

  // 消息配置
  confirmMessages: {
    delete: CONFIRM_MESSAGES.DELETE_USER,
    batchDelete: CONFIRM_MESSAGES.BATCH_DELETE_USERS,
  },

  successMessages: {
    create: SUCCESS_MESSAGES.USER_CREATED,
    update: SUCCESS_MESSAGES.USER_UPDATED,
    delete: SUCCESS_MESSAGES.USER_DELETED,
    batchDelete: SUCCESS_MESSAGES.USER_BATCH_DELETED,
  },
}

// 查看用户详情
function viewUser(user: User) {
  router.push(`/users/${user.id}`)
}
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          用户管理
        </h1>
      </div>
    </div>

    <!-- 使用 EasyCurd 组件 -->
    <EasyCurd :config="config">
      <!-- 自定义用户信息列渲染 -->
      <template #cell-name="{ value, row }">
        <div class="flex items-center">
          <Avatar class="h-10 w-10">
            <AvatarImage :src="row.avatar" />
            <AvatarFallback>{{ (value || '').charAt(0).toUpperCase() }}</AvatarFallback>
          </Avatar>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-900">
              {{ value }}
            </div>
            <div class="text-sm text-gray-500">
              {{ row.email }}
            </div>
            <div
              v-if="row.phone"
              class="text-xs text-gray-400 flex items-center gap-1"
            >
              <Phone class="w-3 h-3" />
              {{ row.phone }}
            </div>
          </div>
        </div>
      </template>

      <!-- 自定义状态列渲染 -->
      <template #cell-status="{ value }">
        <Badge :variant="value === 1 ? 'default' : 'secondary'">
          <div class="flex items-center gap-1">
            <div
              class="w-2 h-2 rounded-full"
              :class="value === 1 ? 'bg-green-500' : 'bg-gray-400'"
            />
            {{ value === 1 ? '激活' : '禁用' }}
          </div>
        </Badge>
      </template>

      <!-- 自定义余额列渲染 -->
      <template #cell-balance="{ value }">
        <div class="text-sm font-medium">
          <span :class="value > 0 ? 'text-green-600' : 'text-gray-400'">
            {{ formatCurrency(value || 0) }}
          </span>
        </div>
      </template>

      <!-- 自定义最近活跃列渲染 -->
      <template #cell-last_active="{ value }">
        <div class="text-sm text-gray-500">
          {{ formatTimeAgo(value) }}
        </div>
      </template>

      <!-- 自定义注册时间列渲染 -->
      <template #cell-createdAt="{ value }">
        <div class="text-sm text-gray-500">
          {{ formatDate(value) }}
        </div>
      </template>

      <!-- 自定义操作列 -->
      <template #before-actions="{ row }">
        <div class="flex items-center justify-end gap-1">
          <Button
            variant="ghost"
            size="sm"
            @click="viewUser(row)"
          >
            <Eye class="w-4 h-4" />
          </Button>
        </div>
      </template>
    </EasyCurd>
  </div>
</template>
