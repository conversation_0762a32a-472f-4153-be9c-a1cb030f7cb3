<script setup lang="ts">
import type { EasyCurdConfig } from '@billing/curd'
import type { User } from '@/api'
import { EasyCurd } from '@billing/curd'
import { Avatar, AvatarFallback, AvatarImage, Badge, Button } from '@billing/ui'
import { Eye, Phone } from 'lucide-vue-next'
import { useRouter } from 'vue-router'
import { userApi } from '@/api'

const router = useRouter()

// EasyCurd 配置
const config: EasyCurdConfig<User> = {
  api: userApi,

  columns: [
    {
      accessorKey: 'name',
      header: '姓名',
      meta: {
        isFormField: true,
        fieldType: 'text',
        required: true,
        rules: [
          { required: true, message: '姓名是必填项' },
          { min: 2, max: 20, message: '姓名长度应在2-20个字符之间' },
        ],
        placeholder: '请输入用户姓名',
      },
    },
    {
      accessorKey: 'email',
      header: '邮箱',
      meta: {
        isFormField: true,
        fieldType: 'email',
        required: true,
        rules: [
          { required: true, message: '邮箱是必填项' },
        ],
      },
    },
    {
      accessorKey: 'password',
      header: '密码',
      meta: {
        isFormField: true,
        fieldType: 'password',
        required: true,
        rules: [
          { required: true, message: '密码是必填项' },
          { min: 6, message: '密码长度不能小于6位' },
        ],
        hideInTable: true,
      },
    },
    {
      accessorKey: 'phone',
      header: '手机号',
      meta: {
        isFormField: true,
        fieldType: 'text',
        placeholder: '请输入手机号',
      },
    },
    {
      accessorKey: 'status',
      header: '状态',
      meta: {
        isFormField: true,
        fieldType: 'select',
        defaultValue: 1,
        options: [
          { label: '激活', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
    {
      accessorKey: 'balance',
      header: '余额',
      meta: {
        fieldType: 'number',
      },
    },
    {
      accessorKey: 'last_active',
      header: '最近活跃',
      meta: { fieldType: 'datetime' },
    },
    {
      accessorKey: 'created_at',
      header: '注册时间',
      meta: { fieldType: 'datetime' },
    },
    {
      accessorKey: 'actions',
      header: '操作',
    },
  ],

  // 基础配置
  primaryKey: 'id',
  pageSize: 20,
  pageSizeOptions: [10, 20, 50, 100],

  // 功能开关
  features: {
    create: true,
    edit: false,
    delete: true,
    batchDelete: true,
    search: true,
  },

  // 消息配置
  confirmMessages: {
    delete: '确定要删除这个用户吗？删除后无法恢复！',
    batchDelete: '确定要删除选中的用户吗？此操作不可撤销！',
  },

  successMessages: {
    create: '用户创建成功！',
    update: '用户信息更新成功！',
    delete: '用户删除成功！',
    batchDelete: '批量删除完成！',
  },
}

// 查看用户详情
function viewUser(user: User) {
  router.push(`/users/${user.id}`)
}

// 格式化时间
function formatDate(dateString: string) {
  if (!dateString)
    return '从未'

  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(dateString))
}

// 格式化货币
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
  }).format(amount)
}

function formatTimeAgo(dateString: string) {
  if (!dateString)
    return '从未'

  const now = new Date()
  const date = new Date(dateString)
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0)
    return '今天'
  if (diffDays === 1)
    return '昨天'
  if (diffDays < 7)
    return `${diffDays}天前`
  if (diffDays < 30)
    return `${Math.floor(diffDays / 7)}周前`
  return `${Math.floor(diffDays / 30)}个月前`
}
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900">
          用户管理
        </h1>
      </div>
    </div>

    <!-- 使用 EasyCurd 组件 -->
    <EasyCurd :config="config">
      <!-- 自定义用户信息列渲染 -->
      <template #cell-name="{ value, row }">
        <div class="flex items-center">
          <Avatar class="h-10 w-10">
            <AvatarImage :src="row.avatar" />
            <AvatarFallback>{{ (value || '').charAt(0).toUpperCase() }}</AvatarFallback>
          </Avatar>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-900">
              {{ value }}
            </div>
            <div class="text-sm text-gray-500">
              {{ row.email }}
            </div>
            <div
              v-if="row.phone"
              class="text-xs text-gray-400 flex items-center gap-1"
            >
              <Phone class="w-3 h-3" />
              {{ row.phone }}
            </div>
          </div>
        </div>
      </template>

      <!-- 自定义状态列渲染 -->
      <template #cell-status="{ value }">
        <Badge :variant="value === 1 ? 'default' : 'secondary'">
          <div class="flex items-center gap-1">
            <div
              class="w-2 h-2 rounded-full"
              :class="value === 1 ? 'bg-green-500' : 'bg-gray-400'"
            />
            {{ value === 1 ? '激活' : '禁用' }}
          </div>
        </Badge>
      </template>

      <!-- 自定义余额列渲染 -->
      <template #cell-balance="{ value }">
        <div class="text-sm font-medium">
          <span :class="value > 0 ? 'text-green-600' : 'text-gray-400'">
            {{ formatCurrency(value || 0) }}
          </span>
        </div>
      </template>

      <!-- 自定义最近活跃列渲染 -->
      <template #cell-last_active="{ value }">
        <div class="text-sm text-gray-500">
          {{ formatTimeAgo(value) }}
        </div>
      </template>

      <!-- 自定义注册时间列渲染 -->
      <template #cell-createdAt="{ value }">
        <div class="text-sm text-gray-500">
          {{ formatDate(value) }}
        </div>
      </template>

      <!-- 自定义操作列 -->
      <template #before-actions="{ row }">
        <div class="flex items-center justify-end gap-1">
          <Button
            variant="ghost"
            size="sm"
            @click="viewUser(row)"
          >
            <Eye class="w-4 h-4" />
          </Button>
        </div>
      </template>
    </EasyCurd>
  </div>
</template>
