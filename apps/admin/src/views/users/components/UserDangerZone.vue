<script setup lang="ts">
import {
  <PERSON>ert,
  AlertDescription,
  AlertTitle,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@billing/ui'
import {
  AlertCircle,
  Trash2,
} from 'lucide-vue-next'

interface Emits {
  (e: 'deleteUser'): void
}

const emit = defineEmits<Emits>()

// 删除用户
function handleDeleteUser() {
  emit('deleteUser')
}
</script>

<template>
  <Card class="border-red-200">
    <CardHeader>
      <CardTitle class="text-red-600">
        危险操作
      </CardTitle>
    </CardHeader>
    <CardContent>
      <Alert class="mb-4">
        <AlertCircle class="h-4 w-4" />
        <AlertTitle>警告</AlertTitle>
        <AlertDescription>
          以下操作不可撤销，请谨慎操作。
        </AlertDescription>
      </Alert>
      <Button
        variant="outline"
        class="w-full text-red-600! border-red-200 hover:bg-red-50"
        @click="handleDeleteUser"
      >
        <Trash2 class="w-4 h-4 mr-2" />
        删除用户
      </Button>
    </CardContent>
  </Card>
</template>
