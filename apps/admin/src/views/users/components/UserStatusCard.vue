<script setup lang="ts">
import type { User } from '@/api'
import {
  Badge,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@billing/ui'
import { computed } from 'vue'

interface Props {
  user: User | null
}

const props = defineProps<Props>()

// 计算属性
const statusBadgeVariant = computed(() => {
  return props.user?.status === 1 ? 'default' : 'secondary'
})

const statusText = computed(() => {
  return props.user?.status === 1 ? '激活' : '禁用'
})

// 格式化时间
function formatDate(dateString: string | number) {
  if (!dateString)
    return '-'
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(dateString))
}

function formatTimeAgo(dateString: string | number) {
  if (!dateString)
    return '-'

  const now = new Date()
  const date = new Date(dateString)
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0)
    return '今天'
  if (diffDays === 1)
    return '昨天'
  if (diffDays < 7)
    return `${diffDays}天前`
  if (diffDays < 30)
    return `${Math.floor(diffDays / 7)}周前`
  return `${Math.floor(diffDays / 30)}个月前`
}

// 格式化货币
function formatCurrency(amount: number) {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2,
  }).format(amount)
}
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle>账户状态</CardTitle>
    </CardHeader>
    <CardContent class="space-y-4">
      <div class="flex justify-between items-center">
        <span class="text-sm text-gray-500">当前状态</span>
        <Badge :variant="statusBadgeVariant">
          {{ statusText }}
        </Badge>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-sm text-gray-500">账户余额</span>
        <span
          class="text-sm font-medium"
          :class="(user?.balance || 0) > 0 ? 'text-green-600' : 'text-gray-500'"
        >
          {{ user ? formatCurrency(user.balance || 0) : '-' }}
        </span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-sm text-gray-500">最近活跃</span>
        <span class="text-sm font-medium">
          {{ user ? formatTimeAgo(user.lastActive) : '-' }}
        </span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-sm text-gray-500">注册时间</span>
        <span class="text-sm font-medium">
          {{ user ? formatDate(user.created_at) : '-' }}
        </span>
      </div>
    </CardContent>
  </Card>
</template>
