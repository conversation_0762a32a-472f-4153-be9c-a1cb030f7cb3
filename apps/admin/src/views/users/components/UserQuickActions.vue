<script setup lang="ts">
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@billing/ui'
import {
  CreditCard,
  RefreshCw,
} from 'lucide-vue-next'

interface Emits {
  (e: 'generatePassword'): void
  (e: 'recharge'): void
}

const emit = defineEmits<Emits>()

// 生成随机密码
function handleGeneratePassword() {
  emit('generatePassword')
}

// 用户充值
function handleRecharge() {
  emit('recharge')
}
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle>快速操作</CardTitle>
    </CardHeader>
    <CardContent class="space-y-3">
      <Button
        variant="outline"
        class="w-full"
        @click="handleRecharge"
      >
        <CreditCard class="w-4 h-4" />
        用户充值
      </Button>
      <Button
        variant="outline"
        class="w-full"
        @click="handleGeneratePassword"
      >
        <RefreshCw class="w-4 h-4" />
        生成随机密码
      </Button>
    </CardContent>
  </Card>
</template>
