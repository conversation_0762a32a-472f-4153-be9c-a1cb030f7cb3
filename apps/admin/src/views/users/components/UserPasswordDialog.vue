<script setup lang="ts">
import {
  Alert,
  AlertDescription,
  AlertTitle,
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@billing/ui'
import {
  AlertCircle,
} from 'lucide-vue-next'

interface Props {
  open: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'confirm'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 关闭对话框
function handleClose() {
  emit('update:open', false)
}

// 确认生成密码
function handleConfirm() {
  emit('confirm')
}
</script>

<template>
  <Dialog
    :open="open"
    @update:open="handleClose"
  >
    <DialogContent>
      <DialogHeader>
        <DialogTitle>生成随机密码确认</DialogTitle>
        <DialogDescription>
          您确定要为用户生成新的随机密码吗？此操作将替换用户当前密码。
        </DialogDescription>
      </DialogHeader>
      <Alert>
        <AlertCircle class="h-4 w-4" />
        <AlertTitle>重要提醒</AlertTitle>
        <AlertDescription>
          <ul class="list-disc list-inside mt-2 space-y-1">
            <li>新密码将自动生成并复制到剪贴板</li>
            <li>用户需要使用新密码重新登录</li>
            <li>请确保及时将新密码告知用户</li>
          </ul>
        </AlertDescription>
      </Alert>
      <DialogFooter>
        <Button
          variant="outline"
          @click="handleClose"
        >
          取消
        </Button>
        <Button @click="handleConfirm">
          确认生成
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
