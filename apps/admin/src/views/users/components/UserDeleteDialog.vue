<script setup lang="ts">
import type { User } from '@/api'
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@billing/ui'

interface Props {
  open: boolean
  user: User | null
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'confirm'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 关闭对话框
function handleClose() {
  emit('update:open', false)
}

// 确认删除
function handleConfirm() {
  emit('confirm')
}
</script>

<template>
  <Dialog
    :open="open"
    @update:open="handleClose"
  >
    <DialogContent>
      <DialogHeader>
        <DialogTitle>删除用户确认</DialogTitle>
        <DialogDescription>
          您即将删除用户 "{{ user?.name }}"，此操作不可撤销。
        </DialogDescription>
      </DialogHeader>
      <DialogFooter>
        <Button
          variant="outline"
          @click="handleClose"
        >
          取消
        </Button>
        <Button
          variant="destructive"
          @click="handleConfirm"
        >
          确认删除
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
