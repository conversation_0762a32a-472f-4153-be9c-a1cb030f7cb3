<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { Input } from '@ui/components/ui/input'
import { cn } from '@ui/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <Input
    data-slot="sidebar-input"
    data-sidebar="input"
    :class="cn(
      'bg-background h-8 w-full shadow-none',
      props.class,
    )"
  >
    <slot />
  </Input>
</template>
