import type { AxiosInstance } from 'axios'
import type { CurdApi, ListParams, ListResponse } from '../types'

import axios from 'axios'

export interface CreateApiOptions {
  baseURL?: string
  axios?: AxiosInstance
  resource: string
}

/**
 * 创建符合 cosy 规范的 CURD API
 */
export function createCosyApi<T = any>(options: CreateApiOptions): CurdApi<T> {
  const { baseURL = '', resource, axios: customAxios } = options

  const client = customAxios || axios.create({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  return {
    // 获取列表
    async getList(params?: ListParams): Promise<ListResponse<T>> {
      const { page = 1, per_page = 20, ...filters } = params || {}

      const response = await client.get(`/${resource}`, {
        params: {
          page,
          per_page,
          ...filters,
        },
      })

      return response.data
    },

    // 获取单个资源
    async getItem(id: string | number): Promise<T> {
      const response = await client.get(`/${resource}/${id}`)
      return response.data
    },

    // 创建资源
    async createItem(data: Partial<T>): Promise<T> {
      const response = await client.post(`/${resource}`, data)
      return response.data
    },

    // 更新资源
    async updateItem(id: string | number, data: Partial<T>): Promise<T> {
      const response = await client.post(`/${resource}/${id}`, data)
      return response.data
    },

    // 删除资源
    async deleteItem(id: string | number, query?: Record<string, any>): Promise<T> {
      return await client.delete(`/${resource}/${id}`, {
        params: query,
      })
    },

    // 恢复资源
    async restoreItem(id: string | number): Promise<T> {
      return await client.patch(`/${resource}/${id}/restore`)
    },
  }
}
