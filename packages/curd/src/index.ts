// API 创建函数
export { createCosyApi } from './api'

// 核心组件
export { default as CurdForm } from './components/CurdForm.vue'
export { default as CurdFormField } from './components/CurdFormField.vue'
export { default as CurdPagination } from './components/CurdPagination.vue'
export { default as CurdTable } from './components/CurdTable.vue'
export { default as EasyCurd } from './components/EasyCurd.vue'
// 字段组件
export * from './components/fields'

export { default as CurdFormCombobox } from './components/fields/Combobox.vue'

// Composables
export { useCurd } from './composables/useCurd'
export { useCurdFormValidation } from './composables/useCurdFormValidation'
export { useFieldLinkage } from './composables/useFieldLinkage'
export { useFormField } from './composables/useFormField'

// 核心类型定义
export type {
  CurdApi,
  CurdConfig,
  CurdHooks,
  CustomRenderSlots,
  EasyCurdConfig,
  ExtendedColumn,
  FieldCascadeConfig,
  FieldLinkageConfig,
  FieldWatchConfig,
  FormFieldConfig,
  FormFieldType,
  ListParams,
  ListResponse,
  Pagination,
  RemoteSearchConfig,
  SelectOption,
  ValidationRule,
} from './types'

// 字段复用检测器（内部使用）
export {
  createFieldReuseDetector,
  FieldReuseDetector,
} from './utils/field-reuse-detector'

// 核心：重新导出 TanStack Table 的完整功能
export * from '@tanstack/vue-table'
