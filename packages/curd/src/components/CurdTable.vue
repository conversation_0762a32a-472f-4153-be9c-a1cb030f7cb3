<script setup lang="ts">
import type {
  ColumnFiltersState,
  RowSelectionState,
  SortingState,
  VisibilityState,
} from '@tanstack/vue-table'
import type { CurdConfig } from '../types'
import {
  Button,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@billing/ui'
import {
  FlexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useVueTable,
} from '@tanstack/vue-table'
import { Edit, Loader2, Plus, RefreshCw, Trash2 } from 'lucide-vue-next'
import { ref } from 'vue'
import { valueUpdater } from '../lib/utils'
import CurdPagination from './CurdPagination.vue'

interface CurdTableProps extends CurdConfig<any> {
  data: any[]
  loading: boolean
  error?: Error | null
  total: number
  currentPage: number
  currentPageSize: number
  totalPages: number
  selectedRowKeys: (string | number)[]
  rowKey: string
  searchParams: Record<string, any>
  sortParams: Record<string, any>
}

const props = withDefaults(defineProps<CurdTableProps>(), {
  primaryKey: 'id',
  searchable: true,
  creatable: true,
  editable: true,
  deletable: true,
  batchDeletable: true,
  recyclable: false,
  pageSizeOptions: () => [10, 20, 50, 100],
  btnText: () => ({
    create: '新建',
    edit: '编辑',
    delete: '删除',
    batchDelete: '批量删除',
    search: '搜索',
    refresh: '刷新',
    export: '导出',
    import: '导入',
  }),
})

const emits = defineEmits<{
  (e: 'refresh'): void
  (e: 'pageChange', page: number): void
  (e: 'pageSizeChange', size: number): void
  (e: 'clickEdit', row: any): void
  (e: 'clickDelete', row: any): void
  (e: 'clickCreate'): void
  (e: 'clickBatchDelete'): void
}>()

// TanStack Table 状态
const sorting = ref<SortingState>([])
const columnFilters = ref<ColumnFiltersState>([])
const columnVisibility = ref<VisibilityState>({})
const rowSelection = ref<RowSelectionState>({})

// 创建 TanStack Table 实例
const table = useVueTable({
  get data() { return props.data },
  get columns() { return props.columns },
  getCoreRowModel: getCoreRowModel(),
  getPaginationRowModel: getPaginationRowModel(),
  getSortedRowModel: getSortedRowModel(),
  getFilteredRowModel: getFilteredRowModel(),
  onSortingChange: updaterOrValue => valueUpdater(updaterOrValue, sorting),
  onColumnFiltersChange: updaterOrValue => valueUpdater(updaterOrValue, columnFilters),
  onColumnVisibilityChange: updaterOrValue => valueUpdater(updaterOrValue, columnVisibility),
  onRowSelectionChange: updaterOrValue => valueUpdater(updaterOrValue, rowSelection),
  state: {
    get sorting() { return sorting.value },
    get columnFilters() { return columnFilters.value },
    get columnVisibility() { return columnVisibility.value },
    get rowSelection() { return rowSelection.value },
  },
  manualPagination: true,
  manualSorting: true,
  manualFiltering: true,
  pageCount: Math.ceil(props.total / props.currentPageSize),
})
</script>

<template>
  <div class="curd-table">
    <!-- 工具栏 -->
    <div class="curd-table-toolbar">
      <div class="curd-table-toolbar-left">
        <slot name="toolbar-left">
          <Button
            v-if="creatable"
            size="sm"
            @click="emits('clickCreate')"
          >
            <Plus class="w-4 h-4" />
            {{ btnText?.create }}
          </Button>

          <Button
            v-if="batchDeletable && selectedRowKeys.length > 0"
            variant="destructive"
            size="sm"
            @click="emits('clickBatchDelete')"
          >
            <Trash2 class="w-4 h-4" />
            {{ btnText?.batchDelete }} ({{ selectedRowKeys.length }})
          </Button>
        </slot>
      </div>

      <div class="curd-table-toolbar-right">
        <slot name="toolbar-right">
          <Button
            variant="outline"
            size="sm"
            @click="emits('refresh')"
          >
            <RefreshCw class="w-4 h-4" />
          </Button>
        </slot>
      </div>
    </div>

    <!-- 表格 -->
    <div class="curd-table-content">
      <Table>
        <TableHeader>
          <!-- 骨架屏 - 当columns为空且loading时显示 -->
          <template v-if="loading && (!columns || columns.length === 0)">
            <TableRow>
              <TableHead
                v-for="i in 5"
                :key="`skeleton-header-${i}`"
              >
                <Skeleton class="h-4 w-20" />
              </TableHead>
            </TableRow>
          </template>

          <!-- 正常表头 -->
          <template v-else>
            <TableRow
              v-for="headerGroup in table.getHeaderGroups()"
              :key="headerGroup.id"
            >
              <TableHead
                v-for="header in headerGroup.headers"
                :key="header.id"
              >
                <!-- 自定义表头插槽 -->
                <slot
                  v-if="$slots[`header-${header.column.id}`]"
                  :name="`header-${header.column.id}`"
                  :header="header"
                  :column="header.column"
                  :context="header.getContext()"
                />
                <!-- 默认表头渲染 -->
                <FlexRender
                  v-else-if="!header.isPlaceholder"
                  :render="header.column.columnDef.header"
                  :props="header.getContext()"
                />
              </TableHead>
            </TableRow>
          </template>
        </TableHeader>

        <TableBody>
          <!-- 骨架屏 - 当columns为空且loading时显示 -->
          <template v-if="loading && (!columns || columns.length === 0)">
            <TableRow
              v-for="i in 5"
              :key="`skeleton-row-${i}`"
            >
              <TableCell
                v-for="j in 5"
                :key="`skeleton-cell-${i}-${j}`"
              >
                <Skeleton class="h-4 w-full" />
              </TableCell>
            </TableRow>
          </template>

          <!-- 普通loading状态 - 当有columns但数据加载中 -->
          <template v-else-if="loading">
            <TableRow>
              <TableCell
                :colspan="table.getAllColumns().length"
                class="text-center py-8"
              >
                <div class="flex items-center justify-center">
                  <Loader2 class="w-6 h-6 animate-spin mr-2" />
                  加载中...
                </div>
              </TableCell>
            </TableRow>
          </template>

          <!-- 无数据状态 -->
          <template v-else-if="table.getRowModel().rows.length === 0">
            <TableRow>
              <TableCell
                :colspan="table.getAllColumns().length"
                class="text-center py-8 text-muted-foreground"
              >
                暂无数据
              </TableCell>
            </TableRow>
          </template>

          <!-- 正常数据行 -->
          <template v-else>
            <TableRow
              v-for="row in table.getRowModel().rows"
              :key="row.id"
              :data-state="row.getIsSelected() ? 'selected' : undefined"
            >
              <TableCell
                v-for="cell in row.getVisibleCells()"
                :key="cell.id"
              >
                <!-- 自定义列单元格插槽 -->
                <slot
                  v-if="$slots[`cell-${cell.column.id}`]"
                  :name="`cell-${cell.column.id}`"
                  :cell="cell"
                  :row="row.original"
                  :value="cell.getValue()"
                  :index="row.index"
                  :column="cell.column"
                  :context="cell.getContext()"
                />
                <!-- 为操作列提供专用插槽支持 -->
                <template v-else-if="cell.column.id === 'actions'">
                  <div class="flex items-center justify-end gap-2 w-fit">
                    <slot
                      name="before-actions"
                      :row="row.original"
                      :index="row.index"
                    />
                    <slot
                      name="actions"
                      :row="row.original"
                      :index="row.index"
                    >
                      <!-- 默认操作按钮 -->
                      <Button
                        v-if="editable"
                        variant="ghost"
                        size="sm"
                        @click="emits('clickEdit', row.original)"
                      >
                        <Edit class="w-4 h-4" />
                      </Button>
                      <Button
                        v-if="deletable"
                        variant="ghost"
                        size="sm"
                        class="text-destructive hover:text-destructive"
                        @click="emits('clickDelete', row.original)"
                      >
                        <Trash2 class="w-4 h-4" />
                      </Button>
                    </slot>

                    <slot
                      name="after-actions"
                      :row="row.original"
                      :index="row.index"
                    />
                  </div>
                </template>

                <!-- 其他列使用 FlexRender -->
                <template v-else>
                  <FlexRender
                    :render="cell.column.columnDef.cell"
                    :props="cell.getContext()"
                  />
                </template>
              </TableCell>
            </TableRow>
          </template>
        </TableBody>
      </Table>
    </div>

    <!-- 分页 -->
    <div
      v-if="total > 0"
      class="curd-table-pagination"
    >
      <CurdPagination
        :current-page="currentPage"
        :page-size="currentPageSize"
        :total="total"
        :page-sizes="pageSizeOptions"
        @update:current-page="emits('pageChange', $event)"
        @update:page-size="emits('pageSizeChange', $event)"
      />
    </div>
  </div>
</template>

<style scoped>
@reference "tailwindcss";

.curd-table {
  @apply space-y-4;
}

.curd-table-toolbar {
  @apply flex items-center justify-between;
}

.curd-table-toolbar-left,
.curd-table-toolbar-right {
  @apply flex items-center gap-2;
}

.curd-table-content {
  @apply rounded-md border;
}

.curd-table-pagination {
  @apply flex items-center justify-between;
}
</style>
