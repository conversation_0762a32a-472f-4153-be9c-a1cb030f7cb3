<script setup lang="ts">
import type { FormFieldConfig } from '../../types'
import {
  Input,
  NumberField as NumberFieldComponent,
  NumberFieldContent,
  NumberFieldDecrement,
  NumberFieldIncrement,
  NumberFieldInput,
  Progress,
  Slider,
} from '@billing/ui'
import { useFormField } from '../../composables/useFormField'
import BaseFormField from './BaseFormField.vue'

interface Props {
  field: FormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

const { isDisabled, getDefaultPlaceholder } = useFormField(props)

// 处理值变化
function handleChange(value: any) {
  emit('update:modelValue', value)
}
</script>

<template>
  <BaseFormField
    :field="field"
    :model-value="modelValue"
    :disabled="disabled"
    :error="error"
    @update:model-value="handleChange"
  >
    <template #default="{ handleChange: onChange }">
      <!-- NumberField 数字输入框 -->
      <NumberFieldComponent
        v-if="field.type === 'number-field'"
        :model-value="modelValue"
        :disabled="isDisabled"
        :min="field.numberConfig?.min"
        :max="field.numberConfig?.max"
        :step="field.numberConfig?.step"
        @update:model-value="onChange"
      >
        <NumberFieldContent>
          <NumberFieldDecrement />
          <NumberFieldInput :placeholder="getDefaultPlaceholder(field)" />
          <NumberFieldIncrement />
        </NumberFieldContent>
      </NumberFieldComponent>

      <!-- Slider 滑块 -->
      <div
        v-else-if="field.type === 'slider'"
        class="space-y-3"
      >
        <div class="flex justify-between text-sm text-muted-foreground">
          <span>{{ field.sliderConfig?.min || 0 }}</span>
          <span class="font-medium">{{ modelValue }}</span>
          <span>{{ field.sliderConfig?.max || 100 }}</span>
        </div>
        <Slider
          :model-value="[modelValue || 0]"
          :disabled="isDisabled"
          :min="field.sliderConfig?.min"
          :max="field.sliderConfig?.max"
          :step="field.sliderConfig?.step"
          @update:model-value="(values) => onChange(values?.[0] ?? 0)"
        />
      </div>

      <!-- Progress 进度条 -->
      <div
        v-else-if="field.type === 'progress'"
        class="space-y-2"
      >
        <div class="flex justify-between text-sm">
          <span>0%</span>
          <span class="font-medium">{{ modelValue || 0 }}%</span>
          <span>100%</span>
        </div>
        <Progress :value="modelValue || 0" />
        <Input
          type="number"
          :model-value="modelValue"
          :disabled="isDisabled"
          min="0"
          max="100"
          @update:model-value="onChange"
        />
      </div>
    </template>
  </BaseFormField>
</template>
