# CURD 字段组件说明

### 🔧 基础组件
- **`BaseFormField.vue`** - 基础字段包装组件，处理标签、错误信息显示

### 📝 字段组件分类

#### 1. **InputField.vue** - 输入类字段
- `text`, `email`, `password`, `number`, `url`, `tel`, `search`, `textarea`

#### 2. **DateField.vue** - 日期类字段
- `date` - 日期选择器（仅日期）
- `datetime` - 日期时间选择器（日期+时间）
- `time` - 时间选择器（仅时间）

#### 3. **SelectField.vue** - 选择类字段
- `select`, `combobox`, `radio-group`, `checkbox-group`

#### 4. **ToggleField.vue** - 开关类字段
- `checkbox`, `switch`

#### 5. **NumberField.vue** - 数字类字段
- `number-field`, `slider`, `progress`

#### 6. **FileField.vue** - 文件上传字段
- `file`, `image`, `file-multiple`, `image-multiple`, `avatar`

#### 7. **SpecialField.vue** - 特殊字段
- `tags-input`, `pin-input`, `calendar`, `color`, `custom`

### 🎯 公共逻辑
- **`useFormField.ts`** - 提取字段公共逻辑的 composable

## 使用方式

根据字段类型动态选择合适的子组件：

```vue
<template>
  <component
    :is="getFieldComponent(field.type)"
    v-if="isVisible"
    :field="field"
    :model-value="modelValue"
    :disabled="disabled"
    :error="error"
    @update:model-value="handleChange"
  />
</template>
```

## 优势

✅ **代码分离** - 每个组件职责单一，易于理解和维护
✅ **复用性高** - 字段组件可以独立使用
✅ **扩展性强** - 新增字段类型只需要添加对应组件
✅ **测试友好** - 可以针对每个字段类型单独测试
✅ **性能优化** - 按需加载字段组件
✅ **用户体验** - 专门的日期组件提供更好的交互体验，不再粗暴地使用普通输入框
