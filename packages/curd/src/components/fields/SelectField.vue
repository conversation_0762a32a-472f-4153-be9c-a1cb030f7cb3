<script setup lang="ts">
import type { FormFieldConfig } from '../../types'
import {
  Checkbox,
  Label,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@billing/ui'
import { computed } from 'vue'
import { useFormField } from '../../composables/useFormField'
import BaseForm<PERSON>ield from './BaseFormField.vue'
import Combobox from './Combobox.vue'

interface FieldState {
  loading: boolean
  error: string | null
  dynamicOptions: { label: string, value: any, [key: string]: any }[]
  dynamicProps: Record<string, any>
}

interface Props {
  field: FormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
  fieldOptions?: { label: string, value: any, [key: string]: any }[]
  fieldState?: FieldState
  dynamicProps?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  fieldOptions: () => [],
  fieldState: () => ({
    loading: false,
    error: null,
    dynamicOptions: [],
    dynamicProps: {},
  }),
  dynamicProps: () => ({}),
})

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

const { isDisabled, getDefaultPlaceholder } = useFormField(props)

// 计算属性：获取实际选项（优先使用动态选项）
const actualOptions = computed(() => {
  // 如果有动态选项（来自联动），优先使用
  if (props.fieldOptions && props.fieldOptions.length > 0) {
    return props.fieldOptions
  }

  // 如果有字段状态中的动态选项，使用它
  if (props.fieldState?.dynamicOptions?.length > 0) {
    return props.fieldState.dynamicOptions
  }

  // 回退到静态选项
  return props.field.options || []
})

// 计算属性：是否正在加载
const isLoading = computed(() => {
  return props.fieldState?.loading || false
})

// 处理值变化
function handleChange(value: any) {
  emit('update:modelValue', value)
}
</script>

<template>
  <BaseFormField
    :field="field"
    :model-value="modelValue"
    :disabled="disabled"
    :error="error"
    @update:model-value="handleChange"
  >
    <template #default="{ handleChange: onChange }">
      <!-- Combobox 可搜索下拉选择 -->
      <Combobox
        v-if="field.type === 'combobox'"
        :field="field"
        :model-value="modelValue"
        :disabled="isDisabled"
        :error="error"
        :field-options="fieldOptions"
        :field-state="fieldState"
        :dynamic-props="dynamicProps"
        @update:model-value="onChange"
      />

      <!-- Select 下拉选择 -->
      <Select
        v-else-if="field.type === 'select'"
        :model-value="modelValue"
        :disabled="isDisabled"
        @update:model-value="onChange"
      >
        <SelectTrigger class="max-w-full">
          <SelectValue :placeholder="getDefaultPlaceholder(field)" />
        </SelectTrigger>
        <SelectContent>
          <!-- 加载状态 -->
          <div
            v-if="isLoading"
            class="flex items-center justify-center py-2"
          >
            <div class="flex items-center gap-2 text-sm text-muted-foreground">
              <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              {{ field.linkage?.loadingPlaceholder || '加载中...' }}
            </div>
          </div>

          <!-- 选项列表 -->
          <template v-else>
            <SelectItem
              v-for="option in actualOptions"
              :key="option.value"
              :value="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </SelectItem>

            <!-- 无选项提示 -->
            <div
              v-if="actualOptions.length === 0"
              class="flex items-center justify-center py-2 text-sm text-muted-foreground"
            >
              暂无可选项
            </div>
          </template>
        </SelectContent>
      </Select>

      <!-- RadioGroup 单选框组 -->
      <RadioGroup
        v-else-if="field.type === 'radio-group'"
        :model-value="modelValue"
        :disabled="isDisabled"
        @update:model-value="onChange"
      >
        <!-- 加载状态 -->
        <div
          v-if="isLoading"
          class="flex items-center gap-2 text-sm text-muted-foreground py-2"
        >
          <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          {{ field.linkage?.loadingPlaceholder || '加载中...' }}
        </div>

        <!-- 选项列表 -->
        <template v-else>
          <div
            v-for="option in actualOptions"
            :key="option.value"
            class="flex items-center space-x-2"
          >
            <RadioGroupItem
              :value="option.value"
              :disabled="option.disabled"
            />
            <Label>{{ option.label }}</Label>
          </div>

          <!-- 无选项提示 -->
          <div
            v-if="actualOptions.length === 0"
            class="text-sm text-muted-foreground py-2"
          >
            暂无可选项
          </div>
        </template>
      </RadioGroup>

      <!-- Checkbox Group 复选框组 -->
      <div
        v-else-if="field.type === 'checkbox-group'"
        class="space-y-2"
      >
        <!-- 加载状态 -->
        <div
          v-if="isLoading"
          class="flex items-center gap-2 text-sm text-muted-foreground py-2"
        >
          <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          {{ field.linkage?.loadingPlaceholder || '加载中...' }}
        </div>

        <!-- 选项列表 -->
        <template v-else>
          <div
            v-for="option in actualOptions"
            :key="option.value"
            class="flex items-center space-x-2"
          >
            <Checkbox
              :checked="(modelValue || []).includes(option.value)"
              :disabled="isDisabled || option.disabled"
              @update:checked="(checked) => {
                const currentValue = modelValue || []
                if (checked) {
                  onChange([...currentValue, option.value])
                }
                else {
                  onChange(currentValue.filter(v => v !== option.value))
                }
              }"
            />
            <Label>{{ option.label }}</Label>
          </div>

          <!-- 无选项提示 -->
          <div
            v-if="actualOptions.length === 0"
            class="text-sm text-muted-foreground py-2"
          >
            暂无可选项
          </div>
        </template>
      </div>
    </template>
  </BaseFormField>
</template>
