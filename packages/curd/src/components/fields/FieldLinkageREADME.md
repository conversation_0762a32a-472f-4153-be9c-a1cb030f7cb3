# 字段联动功能技术实现

## 🎯 功能概述

字段联动功能为 CURD 表单提供了强大的字段间交互能力，支持：

- ✅ **选项联动** - 根据其他字段值动态更新选项列表
- ✅ **值监听** - 监听字段变化执行自定义逻辑
- ✅ **级联更新** - 自动设置关联字段的值
- ✅ **动态属性** - 基于表单数据动态调整字段属性
- ✅ **条件显示** - 根据条件控制字段显示/隐藏
- ✅ **防抖优化** - 避免频繁API调用
- ✅ **加载状态** - 友好的用户体验

## 🏗️ 架构设计

### 核心组件

```
useFieldLinkage (Composable)
    ↓ 提供联动逻辑
CurdForm (表单容器)
    ↓ 集成联动功能
CurdFormField (字段包装器)
    ↓ 传递联动属性
Field Components (具体字段)
    ↓ 实现联动效果
```

### 数据流

```
用户操作 → 字段值变化 → handleFieldChange
    ↓
onFieldChange (useFieldLinkage)
    ↓
并行处理:
├── 级联更新 (cascade)
├── 监听器 (watcher)
├── 依赖字段联动 (linkage)
└── 动态选项/属性更新
    ↓
UI更新 (重新渲染)
```

## 🔧 技术实现

### 1. 核心类型定义

```typescript
// 字段联动配置
interface FieldLinkageConfig {
  dependsOn: string[] // 依赖字段
  handler: (dependentValues, formData) => Promise<Option[]> | Option[]
  clearOnDependentChange?: boolean // 是否清空当前值
  debounceMs?: number // 防抖延迟
  loadingPlaceholder?: string // 加载提示
  errorMessage?: string // 错误信息
}

// 字段监听器配置
interface FieldWatchConfig {
  watchFields: string[] // 监听字段
  handler: (changedField, newValue, oldValue, formData) => void
}

// 级联配置
interface FieldCascadeConfig {
  cascadeMap: Record<string, any> // 值映射
  targetField: string // 目标字段
  forceUpdate?: boolean // 强制更新
}
```

### 2. useFieldLinkage 组合函数

```typescript
export function useFieldLinkage(
  fields: FormFieldConfig[],
  formData: Record<string, any>
) {
  // 状态管理
  const state = reactive<LinkageState>({
    fieldStates: {}, // 字段状态 (loading, error, options)
    dependencyMap: {}, // 依赖关系映射
    watcherMap: {}, // 监听关系映射
    debounceTimers: {} // 防抖定时器
  })

  // 核心方法
  return {
    onFieldChange, // 字段变化处理
    initializeLinkage, // 初始化联动
    getFieldOptions, // 获取字段选项
    getFieldState, // 获取字段状态
    getFieldDynamicProps, // 获取动态属性
    cleanup // 清理资源
  }
}
```

### 3. 字段组件增强

所有字段组件现在支持接收联动相关属性：

```typescript
interface Props {
  field: FormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
  // 新增联动属性
  fieldOptions?: Option[] // 动态选项
  fieldState?: FieldState // 字段状态
  dynamicProps?: Record<string, any> // 动态属性
}
```

### 4. 依赖关系管理

```typescript
// 构建依赖映射
function initFieldStates() {
  fields.forEach((field) => {
    // 联动依赖映射
    if (field.linkage?.dependsOn) {
      field.linkage.dependsOn.forEach((depField) => {
        if (!state.dependencyMap[depField]) {
          state.dependencyMap[depField] = []
        }
        state.dependencyMap[depField].push(key)
      })
    }

    // 监听器映射
    if (field.watcher?.watchFields) {
      field.watcher.watchFields.forEach((watchField) => {
        if (!state.watcherMap[watchField]) {
          state.watcherMap[watchField] = []
        }
        state.watcherMap[watchField].push(key)
      })
    }
  })
}
```

## 📋 字段配置示例

### 基础联动配置

```typescript
const field: FormFieldConfig = {
  key: 'city',
  label: '城市',
  type: 'select',
  linkage: {
    dependsOn: ['province'],
    handler: async ({ province }) => {
      if (!province)
        return []
      return await getCitiesByProvince(province)
    },
    clearOnDependentChange: true,
    debounceMs: 300,
    loadingPlaceholder: '正在加载城市...'
  }
}
```

### 监听器配置

```typescript
const field: FormFieldConfig = {
  key: 'quantity',
  label: '数量',
  type: 'number',
  watcher: {
    watchFields: ['unitPrice'],
    handler: (changedField, newValue, oldValue, formData) => {
      if (changedField === 'unitPrice') {
        formData.totalPrice = formData.quantity * newValue
      }
    }
  }
}
```

### 级联配置

```typescript
const field: FormFieldConfig = {
  key: 'userType',
  label: '用户类型',
  type: 'select',
  cascade: {
    cascadeMap: {
      vip: 'premium',
      normal: 'basic'
    },
    targetField: 'defaultPlan',
    forceUpdate: false
  }
}
```

## 🎨 用户体验优化

### 1. 加载状态指示

```vue
<!-- 选择框加载状态 -->
<div v-if="isLoading" class="flex items-center gap-2">
  <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
  {{ field.linkage?.loadingPlaceholder || '加载中...' }}
</div>
```

### 2. 错误处理

```typescript
try {
  const newOptions = await handler(dependentValues, formData)
  state.fieldStates[fieldKey].dynamicOptions = newOptions
}
catch (error) {
  state.fieldStates[fieldKey].error = field.linkage?.errorMessage || '加载失败'
  state.fieldStates[fieldKey].dynamicOptions = []
}
```

### 3. 防抖优化

```typescript
state.debounceTimers[fieldKey] = setTimeout(async () => {
  await handleFieldLinkage(fieldKey)
}, debounceMs)
```

## 🚀 性能优化

### 1. 依赖映射

预构建依赖关系映射，避免每次变化时遍历所有字段：

```typescript
// O(1) 查找依赖字段
const dependentFields = state.dependencyMap[fieldKey] || []
```

### 2. 防抖机制

避免频繁的API调用和计算：

```typescript
// 清除之前的定时器
if (state.debounceTimers[fieldKey]) {
  clearTimeout(state.debounceTimers[fieldKey])
}
```

### 3. 异步并行处理

```typescript
// 并行处理多个依赖字段的联动
for (const dependentField of dependentFields) {
  await handleFieldLinkage(dependentField)
}
```

## 📝 最佳实践

### 1. 避免循环依赖

❌ **错误示例**：
```typescript
// 字段A依赖字段B，字段B又依赖字段A
fieldA.linkage.dependsOn = ['fieldB']
fieldB.linkage.dependsOn = ['fieldA']
```

✅ **正确示例**：
```typescript
// 建立单向依赖关系
fieldA.linkage.dependsOn = ['fieldB']
fieldC.linkage.dependsOn = ['fieldA']
```

### 2. 合理使用防抖

```typescript
// 对于频繁变化的字段使用较长的防抖时间
linkage: {
  dependsOn: ['searchQuery'],
  handler: searchProducts,
  debounceMs: 500  // 搜索防抖500ms
}
```

### 3. 错误边界处理

```typescript
handler: async (dependentValues) => {
  try {
    return await apiCall(dependentValues)
  }
  catch (error) {
    console.error('联动处理失败:', error)
    return [] // 返回空数组作为回退
  }
}
```

## 🔍 调试技巧

### 1. 开启调试日志

```typescript
// 在 useFieldLinkage 中添加调试信息
console.log('字段联动触发:', {
  changedField: fieldKey,
  newValue,
  dependentFields: state.dependencyMap[fieldKey]
})
```

### 2. 依赖关系可视化

```typescript
// 打印依赖关系映射
console.table(state.dependencyMap)
```

### 3. 性能监控

```typescript
const startTime = performance.now()
await handleFieldLinkage(fieldKey)
const endTime = performance.now()
console.log(`联动处理耗时: ${endTime - startTime}ms`)
```

## 🎉 总结

字段联动功能通过精心设计的架构实现了：

- **高性能** - 依赖映射 + 防抖优化
- **高可用** - 完善的错误处理机制
- **高扩展** - 灵活的配置接口
- **高体验** - 丰富的交互反馈

这套实现为复杂表单场景提供了强大而稳定的解决方案。
