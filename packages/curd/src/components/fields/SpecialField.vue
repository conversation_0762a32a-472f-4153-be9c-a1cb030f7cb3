<script setup lang="ts">
import type { FormFieldConfig } from '../../types'
import {
  Button,
  Calendar,
  Input,
  PinInput,
  PinInputGroup,
  PinInputSeparator,
  PinInputSlot,
  Popover,
  PopoverContent,
  PopoverTrigger,
  TagsInput,
  TagsInputInput,
  TagsInputItem,
  TagsInputItemDelete,
  TagsInputItemText,
} from '@billing/ui'
import { CalendarIcon } from 'lucide-vue-next'
import { ref } from 'vue'
import { useFormField } from '../../composables/useFormField'
import BaseFormField from './BaseFormField.vue'

interface Props {
  field: FormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

const { isDisabled, getDefaultPlaceholder } = useFormField(props)

// 日期选择器状态
const datePickerOpen = ref(false)

// 处理值变化
function handleChange(value: any) {
  emit('update:modelValue', value)
}

// 格式化日期显示
function formatDate(date: Date | string): string {
  if (!date)
    return ''
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN')
}
</script>

<template>
  <BaseFormField
    :field="field"
    :model-value="modelValue"
    :disabled="disabled"
    :error="error"
    @update:model-value="handleChange"
  >
    <template #default="{ handleChange: onChange }">
      <!-- TagsInput 标签输入 -->
      <TagsInput
        v-if="field.type === 'tags-input'"
        :model-value="modelValue || []"
        :disabled="isDisabled"
        :max="field.tagsConfig?.maxTags"
        @update:model-value="onChange"
      >
        <div class="flex gap-2 flex-wrap">
          <TagsInputItem
            v-for="item in modelValue || []"
            :key="item"
            :value="item"
          >
            <TagsInputItemText />
            <TagsInputItemDelete />
          </TagsInputItem>
        </div>

        <TagsInputInput :placeholder="getDefaultPlaceholder(field)" />
      </TagsInput>

      <!-- PinInput PIN码输入 -->
      <PinInput
        v-else-if="field.type === 'pin-input'"
        :model-value="modelValue"
        :disabled="isDisabled"
        :mask="field.pinConfig?.mask"
        @update:model-value="onChange"
      >
        <PinInputGroup>
          <template
            v-for="(id, index) in (field.pinConfig?.length || 4)"
            :key="id"
          >
            <PinInputSlot :index="index" />
            <PinInputSeparator v-if="index !== (field.pinConfig?.length || 4) - 1" />
          </template>
        </PinInputGroup>
      </PinInput>

      <!-- Calendar 日历选择器 -->
      <Popover
        v-else-if="field.type === 'calendar'"
        v-model:open="datePickerOpen"
      >
        <PopoverTrigger as-child>
          <Button
            variant="outline"
            class="w-full justify-start text-left font-normal"
            :class="{
              'text-muted-foreground': !modelValue,
            }"
            :disabled="isDisabled"
          >
            <CalendarIcon class="mr-2 h-4 w-4" />
            {{ modelValue ? formatDate(modelValue) : getDefaultPlaceholder(field) }}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          class="w-auto p-0"
          align="start"
        >
          <Calendar
            :model-value="modelValue"
            @update:model-value="(date) => {
              onChange(date)
              datePickerOpen = false
            }"
          />
        </PopoverContent>
      </Popover>

      <!-- 颜色选择器 -->
      <div
        v-else-if="field.type === 'color'"
        class="flex items-center gap-2"
      >
        <Input
          type="color"
          :model-value="modelValue"
          :disabled="isDisabled"
          class="w-12 h-10 p-1 border rounded"
          @update:model-value="onChange"
        />
        <Input
          :model-value="modelValue"
          :disabled="isDisabled"
          :placeholder="getDefaultPlaceholder(field)"
          @update:model-value="onChange"
        />
      </div>

      <!-- 自定义组件 -->
      <component
        :is="field.customComponent.component"
        v-else-if="field.type === 'custom' && field.customComponent"
        :model-value="modelValue"
        :disabled="isDisabled"
        v-bind="field.customComponent.props"
        @update:model-value="onChange"
      />
    </template>
  </BaseFormField>
</template>
