# 字段联动功能使用示例

## 📋 概述

字段联动功能允许表单字段之间建立依赖关系，实现动态的交互效果，如：
- 省市县三级联动
- 根据产品类型动态加载规格选项
- 根据开关状态控制其他字段的显示/隐藏
- 字段值的自动级联更新

## 🔄 联动类型

### 1. 选项联动 (linkage)
根据其他字段的值动态更新当前字段的选项列表。

### 2. 值监听 (watcher)
监听其他字段的值变化并执行自定义逻辑。

### 3. 级联更新 (cascade)
当前字段值变化时自动设置其他字段的值。

### 4. 动态选项 (dynamicOptions)
基于整个表单数据动态生成选项。

### 5. 动态属性 (dynamicProps)
基于表单数据动态设置字段属性。

## 💻 使用示例

### 示例1：省市县三级联动

```typescript
import type { FormFieldConfig } from '@billing/curd'

// 模拟API调用
const cityApi = {
  getCitiesByProvince: async (provinceId: string) => {
    // 模拟异步获取城市列表
    const cityMap: Record<string, any[]> = {
      guangdong: [
        { label: '广州市', value: 'guangzhou' },
        { label: '深圳市', value: 'shenzhen' },
        { label: '珠海市', value: 'zhuhai' }
      ],
      jiangsu: [
        { label: '南京市', value: 'nanjing' },
        { label: '苏州市', value: 'suzhou' },
        { label: '无锡市', value: 'wuxi' }
      ]
    }

    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟
    return cityMap[provinceId] || []
  },

  getDistrictsByCity: async (cityId: string) => {
    const districtMap: Record<string, any[]> = {
      guangzhou: [
        { label: '天河区', value: 'tianhe' },
        { label: '海珠区', value: 'haizhu' }
      ],
      shenzhen: [
        { label: '南山区', value: 'nanshan' },
        { label: '福田区', value: 'futian' }
      ]
    }

    await new Promise(resolve => setTimeout(resolve, 300))
    return districtMap[cityId] || []
  }
}

const addressFields: FormFieldConfig[] = [
  {
    key: 'province',
    label: '省份',
    type: 'select',
    required: true,
    options: [
      { label: '广东省', value: 'guangdong' },
      { label: '江苏省', value: 'jiangsu' },
      { label: '浙江省', value: 'zhejiang' }
    ]
  },
  {
    key: 'city',
    label: '城市',
    type: 'select',
    required: true,
    placeholder: '请先选择省份',
    linkage: {
      dependsOn: ['province'],
      handler: async (dependentValues) => {
        const { province } = dependentValues
        if (!province)
          return []

        return await cityApi.getCitiesByProvince(province)
      },
      clearOnDependentChange: true,
      debounceMs: 200,
      loadingPlaceholder: '正在加载城市...',
      errorMessage: '加载城市失败，请重试'
    }
  },
  {
    key: 'district',
    label: '区县',
    type: 'select',
    required: true,
    placeholder: '请先选择城市',
    linkage: {
      dependsOn: ['city'],
      handler: async (dependentValues) => {
        const { city } = dependentValues
        if (!city)
          return []

        return await cityApi.getDistrictsByCity(city)
      },
      clearOnDependentChange: true,
      loadingPlaceholder: '正在加载区县...'
    }
  }
]
```

### 示例2：产品规格联动

```typescript
const productFields: FormFieldConfig[] = [
  {
    key: 'productType',
    label: '产品类型',
    type: 'select',
    required: true,
    options: [
      { label: '电子产品', value: 'electronics' },
      { label: '服装', value: 'clothing' },
      { label: '家具', value: 'furniture' }
    ]
  },
  {
    key: 'specifications',
    label: '产品规格',
    type: 'checkbox-group',
    required: true,
    linkage: {
      dependsOn: ['productType'],
      handler: async (dependentValues) => {
        const { productType } = dependentValues

        const specMap: Record<string, any[]> = {
          electronics: [
            { label: '高端', value: 'high_end' },
            { label: '中端', value: 'mid_range' },
            { label: '入门', value: 'entry_level' }
          ],
          clothing: [
            { label: 'S码', value: 'size_s' },
            { label: 'M码', value: 'size_m' },
            { label: 'L码', value: 'size_l' },
            { label: 'XL码', value: 'size_xl' }
          ],
          furniture: [
            { label: '单人', value: 'single' },
            { label: '双人', value: 'double' },
            { label: '多人', value: 'multiple' }
          ]
        }

        return specMap[productType] || []
      }
    }
  }
]
```

### 示例3：条件显示联动

```typescript
const settingsFields: FormFieldConfig[] = [
  {
    key: 'enableNotification',
    label: '启用通知',
    type: 'switch',
    defaultValue: false
  },
  {
    key: 'notificationEmail',
    label: '通知邮箱',
    type: 'email',
    required: true,
    show: formData => formData.enableNotification, // 根据开关状态显示/隐藏
    placeholder: '请输入邮箱地址'
  },
  {
    key: 'notificationFrequency',
    label: '通知频率',
    type: 'select',
    show: formData => formData.enableNotification,
    options: [
      { label: '实时', value: 'realtime' },
      { label: '每小时', value: 'hourly' },
      { label: '每日', value: 'daily' }
    ],
    defaultValue: 'daily'
  }
]
```

### 示例4：级联更新

```typescript
const userFields: FormFieldConfig[] = [
  {
    key: 'userType',
    label: '用户类型',
    type: 'select',
    required: true,
    options: [
      { label: 'VIP用户', value: 'vip' },
      { label: '普通用户', value: 'normal' },
      { label: '企业用户', value: 'enterprise' }
    ],
    cascade: {
      cascadeMap: {
        vip: 'premium',
        normal: 'basic',
        enterprise: 'business'
      },
      targetField: 'defaultPlan',
      forceUpdate: false // 只有当目标字段为空时才更新
    }
  },
  {
    key: 'defaultPlan',
    label: '默认方案',
    type: 'select',
    options: [
      { label: '基础方案', value: 'basic' },
      { label: '高级方案', value: 'premium' },
      { label: '企业方案', value: 'business' }
    ]
  }
]
```

### 示例5：字段监听器

```typescript
const orderFields: FormFieldConfig[] = [
  {
    key: 'quantity',
    label: '数量',
    type: 'number',
    min: 1,
    required: true,
    watcher: {
      watchFields: ['unitPrice'],
      handler: (changedField, newValue, oldValue, formData) => {
        // 当单价变化时，重新计算总价
        if (changedField === 'unitPrice' && formData.quantity) {
          formData.totalPrice = formData.quantity * newValue
        }
      }
    }
  },
  {
    key: 'unitPrice',
    label: '单价',
    type: 'number',
    min: 0,
    step: 0.01,
    required: true,
    watcher: {
      watchFields: ['quantity'],
      handler: (changedField, newValue, oldValue, formData) => {
        // 当数量变化时，重新计算总价
        if (changedField === 'quantity' && formData.unitPrice) {
          formData.totalPrice = newValue * formData.unitPrice
        }
      }
    }
  },
  {
    key: 'totalPrice',
    label: '总价',
    type: 'number',
    disabled: true, // 只读字段
    placeholder: '自动计算'
  }
]
```

### 示例6：动态选项和属性

```typescript
const dynamicFields: FormFieldConfig[] = [
  {
    key: 'category',
    label: '分类',
    type: 'select',
    options: [
      { label: '电子产品', value: 'electronics' },
      { label: '图书', value: 'books' },
      { label: '服装', value: 'clothing' }
    ]
  },
  {
    key: 'subcategory',
    label: '子分类',
    type: 'select',
    dynamicOptions: async (formData) => {
      const { category } = formData
      if (!category)
        return []

      // 基于分类动态获取子分类
      const subcategoryMap: Record<string, any[]> = {
        electronics: [
          { label: '手机', value: 'phone' },
          { label: '电脑', value: 'computer' },
          { label: '耳机', value: 'headphone' }
        ],
        books: [
          { label: '小说', value: 'novel' },
          { label: '技术', value: 'technical' },
          { label: '教育', value: 'education' }
        ],
        clothing: [
          { label: '上衣', value: 'top' },
          { label: '裤子', value: 'pants' },
          { label: '鞋子', value: 'shoes' }
        ]
      }

      return subcategoryMap[category] || []
    }
  },
  {
    key: 'price',
    label: '价格',
    type: 'number',
    dynamicProps: (formData) => {
      const { category } = formData

      // 根据分类动态设置价格输入框的属性
      const propsMap: Record<string, any> = {
        electronics: { min: 100, max: 50000, step: 100, placeholder: '请输入价格（100-50000）' },
        books: { min: 10, max: 1000, step: 5, placeholder: '请输入价格（10-1000）' },
        clothing: { min: 50, max: 5000, step: 10, placeholder: '请输入价格（50-5000）' }
      }

      return propsMap[category] || { min: 0, step: 1, placeholder: '请输入价格' }
    }
  }
]
```

## 🎯 最佳实践

### 1. 性能优化
- 使用防抖（debounce）避免频繁的API调用
- 合理设置 `clearOnDependentChange` 避免不必要的字段清空
- 对复杂的联动逻辑使用 `nextTick` 确保DOM更新完成

### 2. 用户体验
- 提供加载状态提示
- 设置合适的错误提示信息
- 使用占位符引导用户操作顺序

### 3. 错误处理
- 在联动函数中添加 try-catch 处理异常
- 提供友好的错误提示
- 设置回退选项

### 4. 代码组织
- 将复杂的联动逻辑提取为独立的服务函数
- 使用 TypeScript 确保类型安全
- 为联动配置添加详细的注释

## 📝 注意事项

1. **避免循环依赖**：确保字段之间的依赖关系不会形成循环
2. **性能考虑**：避免在联动函数中执行重复的计算或API调用
3. **数据一致性**：确保联动更新不会破坏表单数据的一致性
4. **用户友好**：提供清晰的视觉反馈和操作引导

## 🔧 技术实现

字段联动功能通过以下组件实现：

- `useFieldLinkage` - 核心联动逻辑组合函数
- `CurdForm` - 集成联动功能的表单组件
- `CurdFormField` - 支持联动属性传递的字段包装器
- 各种字段组件 - 支持动态选项和属性的具体实现

通过这些组件的协作，实现了灵活、高效的字段联动功能。
