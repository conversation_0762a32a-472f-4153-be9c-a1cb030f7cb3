import type { FormFieldConfig } from '../types'
import { computed } from 'vue'

export interface UseFormFieldProps {
  field: FormFieldConfig
  modelValue: any
  disabled?: boolean
  error?: string
}

export function useFormField(props: UseFormFieldProps) {
  // 计算属性：是否显示必填标识
  const showRequired = computed(() => props.field.required)

  // 计算属性：字段是否可见
  const isVisible = computed(() => {
    if (typeof props.field.show === 'function') {
      return props.field.show({})
    }
    return props.field.show !== false
  })

  // 计算属性：字段是否禁用
  const isDisabled = computed(() => {
    if (props.disabled)
      return true
    if (typeof props.field.disabled === 'function') {
      return props.field.disabled({})
    }
    return props.field.disabled === true
  })

  // 获取字段属性
  function getFieldProps(field: FormFieldConfig) {
    const baseProps: Record<string, any> = {
      placeholder: field.placeholder || getDefaultPlaceholder(field),
      ...field.componentProps,
    }

    // 设置输入类型
    if (field.type === 'number') {
      baseProps.type = 'number'
    }
    else if (field.type === 'email') {
      baseProps.type = 'email'
    }
    else if (field.type === 'password') {
      baseProps.type = 'password'
    }
    else if (field.type === 'url') {
      baseProps.type = 'url'
    }
    else if (field.type === 'tel') {
      baseProps.type = 'tel'
    }
    else if (field.type === 'search') {
      baseProps.type = 'search'
    }
    else if (field.type === 'date') {
      baseProps.type = 'date'
    }
    else if (field.type === 'datetime') {
      baseProps.type = 'datetime-local'
    }
    else if (field.type === 'time') {
      baseProps.type = 'time'
    }

    return baseProps
  }

  // 获取默认占位符
  function getDefaultPlaceholder(field: FormFieldConfig): string {
    switch (field.type) {
      case 'select':
      case 'combobox':
        return `请选择${field.label}`
      case 'date':
        return '请选择日期'
      case 'datetime':
        return '请选择日期时间'
      case 'time':
        return '请选择时间'
      case 'file':
      case 'image':
        return '请选择文件'
      case 'tags-input':
        return '输入后按回车添加标签'
      case 'color':
        return '请选择颜色'
      default:
        return `请输入${field.label}`
    }
  }

  return {
    showRequired,
    isVisible,
    isDisabled,
    getFieldProps,
    getDefaultPlaceholder,
  }
}
