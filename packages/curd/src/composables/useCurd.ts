import type { Ref } from 'vue'
import type { CurdConfig, CurdHooks, ListParams } from '../types'
import { ref, watch } from 'vue'

export interface UseCurdOptions<T> extends CurdConfig<T> {
  hooks?: CurdHooks<T>
  immediate?: boolean
}

export interface UseCurdFunction<T> {
  refresh: () => Promise<void>
  search: (params?: Record<string, any>) => Promise<void>
  sort: (field: string, order?: 'asc' | 'desc') => Promise<void>
  changePage: (page: number) => Promise<void>
  changePageSize: (size: number) => Promise<void>
  create: (data: Partial<T>) => Promise<T>
  update: (id: string | number, data: Partial<T>) => Promise<T>
  remove: (id: string | number, permanently?: boolean) => Promise<void>
  batchRemove: (ids: (string | number)[], permanently?: boolean) => Promise<void>
  clearSelection: () => void
}

export interface UseCurdReturn<T = any> extends UseCurdFunction<T> {
  // 数据状态
  data: Ref<T[]>
  loading: Ref<boolean>
  error: Ref<Error | null>
  total: Ref<number>
  currentPage: Ref<number>
  currentPageSize: Ref<number>
  totalPages: Ref<number>

  // 选中状态
  selectedRows: Ref<T[]>
  selectedRowKeys: Ref<(string | number)[]>

  // 搜索和排序状态
  searchParams: Ref<Record<string, any>>
  sortParams: Ref<{ field?: string, order?: 'asc' | 'desc' }>
}

// 辅助函数：执行钩子并处理中断逻辑
async function executeHook<T extends (...args: any[]) => any>(
  hook: T | undefined,
  ...args: Parameters<T>
): Promise<{ shouldContinue: boolean, result?: any }> {
  if (!hook) {
    return { shouldContinue: true }
  }

  try {
    const result = await hook(...args)
    if (result === false) {
      return { shouldContinue: false }
    }
    return { shouldContinue: true, result }
  }
  catch (error) {
    console.error('Hook execution failed:', error)
    return { shouldContinue: false }
  }
}

// 辅助函数：处理错误钩子
async function handleError(
  hooks: CurdHooks<any> | undefined,
  error: Error,
  operation: string,
  context?: any,
): Promise<void> {
  if (hooks?.onError) {
    try {
      await hooks.onError(error, operation, context)
    }
    catch (hookError) {
      console.error('Error in onError hook:', hookError)
    }
  }
}

export function useCurd<T extends Record<string, any>>(
  options: UseCurdOptions<T>,
): UseCurdReturn<T> {
  const {
    api,
    pageSize: defaultPageSize = 20,
    hooks = {},
    immediate = true,
  } = options

  // 状态
  const data = ref<T[]>([]) as Ref<T[]>
  const loading = ref(false)
  const error = ref<Error | null>(null)
  const total = ref(0)
  const currentPage = ref(1)
  const currentPageSize = ref(defaultPageSize)
  const totalPages = ref(0)
  const searchParams = ref<Record<string, any>>({})
  const sortParams = ref<{ field?: string, order?: 'asc' | 'desc' }>({})

  // 选中状态
  const selectedRows = ref<T[]>([]) as Ref<T[]>
  const selectedRowKeys = ref<(string | number)[]>([]) as Ref<(string | number)[]>

  // 获取列表数据
  const fetchList = async () => {
    try {
      loading.value = true
      error.value = null

      // 执行 beforeRefresh 钩子
      const refreshHookResult = await executeHook(hooks.beforeRefresh)
      if (!refreshHookResult.shouldContinue) {
        return
      }

      let params: ListParams = {
        page: currentPage.value,
        page_size: currentPageSize.value,
        ...searchParams.value,
      }

      // 添加排序参数
      if (sortParams.value.field) {
        params.sort_field = sortParams.value.field
        params.sort_order = sortParams.value.order || 'asc'
      }

      // 执行 beforeList 钩子
      const listHookResult = await executeHook(hooks.beforeList, params)
      if (!listHookResult.shouldContinue) {
        return
      }
      if (listHookResult.result !== undefined && listHookResult.result !== false) {
        params = listHookResult.result as ListParams
      }

      const response = await api.getList(params)

      // 执行 afterList 钩子
      const afterListResult = await executeHook(hooks.afterList, response)
      if (!afterListResult.shouldContinue) {
        return
      }

      data.value = response.data
      total.value = response.pagination?.total || 0
      totalPages.value = response.pagination?.total_pages || 0
      currentPage.value = response.pagination?.current_page || 1

      // 执行 afterRefresh 钩子
      await executeHook(hooks.afterRefresh, response.data)
    }
    catch (err) {
      const errorObj = err as Error
      error.value = errorObj
      await handleError(hooks, errorObj, 'fetchList')
      console.error('Failed to fetch list:', err)
    }
    finally {
      loading.value = false
    }
  }

  // 刷新列表
  const refresh = async () => {
    await fetchList()
  }

  // 搜索
  const search = async (params: Record<string, any> = {}) => {
    try {
      // 执行 beforeSearch 钩子
      const searchHookResult = await executeHook(hooks.beforeSearch, params)
      if (!searchHookResult.shouldContinue) {
        return
      }
      if (searchHookResult.result !== undefined && searchHookResult.result !== false) {
        params = searchHookResult.result as Record<string, any>
      }

      searchParams.value = params
      currentPage.value = 1
      await fetchList()

      // 执行 afterSearch 钩子
      await executeHook(hooks.afterSearch, params, data.value)
    }
    catch (err) {
      const errorObj = err as Error
      await handleError(hooks, errorObj, 'search', { params })
      throw err
    }
  }

  // 排序
  const sort = async (field: string, order: 'asc' | 'desc' = 'asc') => {
    try {
      // 如果是同一个字段，切换排序顺序
      if (sortParams.value.field === field) {
        if (sortParams.value.order === 'asc') {
          order = 'desc'
        }
        else if (sortParams.value.order === 'desc') {
          // 如果已经是降序，则清除排序
          sortParams.value = {}
          await fetchList()
          return
        }
      }

      // 执行 beforeSort 钩子
      const sortHookResult = await executeHook(hooks.beforeSort, field, order)
      if (!sortHookResult.shouldContinue) {
        return
      }
      if (sortHookResult.result !== undefined && sortHookResult.result !== false) {
        ({ field, order } = sortHookResult.result as { field: string, order: 'asc' | 'desc' })
      }

      sortParams.value = { field, order }
      currentPage.value = 1
      await fetchList()

      // 执行 afterSort 钩子
      await executeHook(hooks.afterSort, field, order, data.value)
    }
    catch (err) {
      const errorObj = err as Error
      await handleError(hooks, errorObj, 'sort', { field, order })
      throw err
    }
  }

  // 切换页码
  const changePage = async (page: number) => {
    if (page === currentPage.value) {
      return
    }
    currentPage.value = page
    await fetchList()
  }

  // 切换每页条数
  const changePageSize = async (size: number) => {
    currentPageSize.value = size
    currentPage.value = 1
    await fetchList()
  }

  // 创建
  const create = async (formData: Partial<T>): Promise<T> => {
    try {
      let data = formData

      // 执行 beforeSave 钩子
      const beforeSaveResult = await executeHook(hooks.beforeSave, data, 'create')
      if (!beforeSaveResult.shouldContinue) {
        throw new Error('操作被 beforeSave 钩子中断')
      }
      if (beforeSaveResult.result !== undefined && beforeSaveResult.result !== false) {
        data = beforeSaveResult.result as Partial<T>
      }

      // 执行 beforeCreate 钩子
      const createHookResult = await executeHook(hooks.beforeCreate, data)
      if (!createHookResult.shouldContinue) {
        throw new Error('操作被 beforeCreate 钩子中断')
      }
      if (createHookResult.result !== undefined && createHookResult.result !== false) {
        data = createHookResult.result as Partial<T>
      }

      const result = await api.createItem(data)

      // 执行 afterCreate 钩子
      const afterCreateResult = await executeHook(hooks.afterCreate, result)
      if (!afterCreateResult.shouldContinue) {
        console.warn('afterCreate 钩子返回 false，但创建操作已完成')
      }

      // 执行 afterSave 钩子
      const afterSaveResult = await executeHook(hooks.afterSave, result, 'create')
      if (!afterSaveResult.shouldContinue) {
        console.warn('afterSave 钩子返回 false，但创建操作已完成')
      }

      // 刷新列表
      await refresh()

      return result
    }
    catch (err) {
      const errorObj = err as Error
      await handleError(hooks, errorObj, 'create', { data: formData })
      throw err
    }
  }

  // 更新
  const update = async (id: string | number, formData: Partial<T>): Promise<T> => {
    try {
      let data = formData

      // 执行 beforeSave 钩子
      const beforeSaveResult = await executeHook(hooks.beforeSave, data, 'update', id)
      if (!beforeSaveResult.shouldContinue) {
        throw new Error('操作被 beforeSave 钩子中断')
      }
      if (beforeSaveResult.result !== undefined && beforeSaveResult.result !== false) {
        data = beforeSaveResult.result as Partial<T>
      }

      // 执行 beforeUpdate 钩子
      const updateHookResult = await executeHook(hooks.beforeUpdate, id, data)
      if (!updateHookResult.shouldContinue) {
        throw new Error('操作被 beforeUpdate 钩子中断')
      }
      if (updateHookResult.result !== undefined && updateHookResult.result !== false) {
        data = updateHookResult.result as Partial<T>
      }

      const result = await api.updateItem(id, data)

      // 执行 afterUpdate 钩子
      const afterUpdateResult = await executeHook(hooks.afterUpdate, result)
      if (!afterUpdateResult.shouldContinue) {
        console.warn('afterUpdate 钩子返回 false，但更新操作已完成')
      }

      // 执行 afterSave 钩子
      const afterSaveResult = await executeHook(hooks.afterSave, result, 'update')
      if (!afterSaveResult.shouldContinue) {
        console.warn('afterSave 钩子返回 false，但更新操作已完成')
      }

      // 刷新列表
      await refresh()

      return result
    }
    catch (err) {
      const errorObj = err as Error
      await handleError(hooks, errorObj, 'update', { id, data: formData })
      throw err
    }
  }

  // 删除
  const remove = async (id: string | number, permanently = false): Promise<void> => {
    try {
      // 执行 beforeDelete 钩子
      const deleteHookResult = await executeHook(hooks.beforeDelete, id)
      if (!deleteHookResult.shouldContinue) {
        throw new Error('操作被 beforeDelete 钩子中断')
      }

      await api.deleteItem(id, { permanently })

      // 执行 afterDelete 钩子
      const afterDeleteResult = await executeHook(hooks.afterDelete, id)
      if (!afterDeleteResult.shouldContinue) {
        console.warn('afterDelete 钩子返回 false，但删除操作已完成')
      }

      // 刷新列表
      await refresh()
    }
    catch (err) {
      const errorObj = err as Error
      await handleError(hooks, errorObj, 'delete', { id, permanently })
      throw err
    }
  }

  // 批量删除
  const batchRemove = async (ids: (string | number)[], permanently = false): Promise<void> => {
    try {
      // 执行 beforeBatchDelete 钩子
      const batchDeleteHookResult = await executeHook(hooks.beforeBatchDelete, ids)
      if (!batchDeleteHookResult.shouldContinue) {
        throw new Error('操作被 beforeBatchDelete 钩子中断')
      }

      await Promise.all(ids.map(id => api.deleteItem(id, { permanently })))

      // 执行 afterBatchDelete 钩子
      const afterBatchDeleteResult = await executeHook(hooks.afterBatchDelete, ids)
      if (!afterBatchDeleteResult.shouldContinue) {
        console.warn('afterBatchDelete 钩子返回 false，但批量删除操作已完成')
      }

      await refresh()
    }
    catch (err) {
      const errorObj = err as Error
      await handleError(hooks, errorObj, 'batchDelete', { ids, permanently })
      throw err
    }
  }

  // 清空选中
  const clearSelection = () => {
    selectedRowKeys.value = []
    selectedRows.value = []
  }

  // 监听选中状态变化，触发相关钩子
  watch(selectedRowKeys, async (newKeys, oldKeys) => {
    if (newKeys.length !== oldKeys.length || newKeys.some((key, index) => key !== oldKeys[index])) {
      // 执行 beforeSelect 钩子
      const beforeSelectResult = await executeHook(hooks.beforeSelect, newKeys)
      if (!beforeSelectResult.shouldContinue) {
        // 如果钩子返回 false，恢复之前的选中状态
        selectedRowKeys.value = oldKeys
        return
      }

      // 更新选中的行数据
      selectedRows.value = data.value.filter(row =>
        newKeys.includes(row[options.primaryKey || 'id']),
      )

      // 执行 afterSelect 钩子
      await executeHook(hooks.afterSelect, newKeys, selectedRows.value)
    }
  })

  // 立即加载
  if (immediate) {
    fetchList()
  }

  return {
    // 数据状态
    data,
    loading,
    error,
    total,
    currentPage,
    currentPageSize,
    totalPages,

    // 选中状态
    selectedRows,
    selectedRowKeys,

    // 搜索和排序状态
    searchParams,
    sortParams,

    // 操作方法
    refresh,
    search,
    sort,
    changePage,
    changePageSize,
    create,
    update,
    remove,
    batchRemove,
    clearSelection,
  }
}
