import type { FormFieldConfig } from '../types'
import { reactive } from 'vue'

export function useCurdFormValidation() {
  const errors = reactive<Record<string, string>>({})

  // 验证单个字段
  function validateField(field: FormFieldConfig, value: any, formData: Record<string, any>): string | null {
    const key = field.key as string

    // 必填验证
    if (field.required) {
      if (value === undefined || value === null || value === '') {
        return `${field.label}是必填项`
      }
    }

    // 类型验证
    if (value !== '' && value !== null && value !== undefined) {
      switch (field.type) {
        case 'email': {
          const emailRegex = /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/
          if (!emailRegex.test(value)) {
            return '请输入有效的邮箱地址'
          }
          break
        }
        case 'number': {
          if (Number.isNaN(Number(value))) {
            return '请输入有效的数字'
          }
          break
        }
      }
    }

    // 自定义验证规则
    if (field.rules) {
      for (const rule of field.rules) {
        if (rule.required && (value === undefined || value === null || value === '')) {
          return rule.message || `${field.label}是必填项`
        }

        if (value !== '' && value !== null && value !== undefined) {
          if (rule.min !== undefined) {
            if (field.type === 'number') {
              if (Number(value) < rule.min) {
                return rule.message || `最小值为 ${rule.min}`
              }
            }
            else if (typeof value === 'string' && value.length < rule.min) {
              return rule.message || `最少 ${rule.min} 个字符`
            }
          }

          if (rule.max !== undefined) {
            if (field.type === 'number') {
              if (Number(value) > rule.max) {
                return rule.message || `最大值为 ${rule.max}`
              }
            }
            else if (typeof value === 'string' && value.length > rule.max) {
              return rule.message || `最多 ${rule.max} 个字符`
            }
          }

          if (rule.pattern && typeof value === 'string') {
            if (!rule.pattern.test(value)) {
              return rule.message || '格式不正确'
            }
          }

          if (rule.validator) {
            const result = rule.validator(value, formData)
            if (typeof result === 'string') {
              return result
            }
            if (result === false) {
              return rule.message || '验证失败'
            }
          }
        }
      }
    }

    return null
  }

  // 验证所有字段
  function validateForm(fields: FormFieldConfig[], formData: Record<string, any>): boolean {
    let isValid = true
    clearErrors()

    fields.forEach((field) => {
      const value = formData[field.key as string]
      const error = validateField(field, value, formData)
      if (error) {
        setError(field.key as string, error)
        isValid = false
      }
    })

    return isValid
  }

  // 设置字段错误
  function setError(key: string, message: string) {
    errors[key] = message
  }

  // 清除字段错误
  function clearError(key: string) {
    if (errors[key]) {
      delete errors[key]
    }
  }

  // 清除所有错误
  function clearErrors() {
    Object.keys(errors).forEach(key => delete errors[key])
  }

  // 获取字段错误
  function getError(key: string): string | undefined {
    return errors[key]
  }

  // 检查是否有错误
  function hasErrors(): boolean {
    return Object.keys(errors).length > 0
  }

  return {
    errors,
    validateField,
    validateForm,
    setError,
    clearError,
    clearErrors,
    getError,
    hasErrors,
  }
}
