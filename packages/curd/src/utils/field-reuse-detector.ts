import type { ColumnDef } from '@tanstack/vue-table'
import type { FormFieldConfig, FormFieldType } from '../types'

/**
 * 字段复用检测器
 * 自动检测 FormField 和 Column 之间的重复字段并进行智能转换
 */
export class FieldReuseDetector<T extends Record<string, any> = any> {
  /**
   * 从表单字段自动生成表格列配置
   */
  generateColumnsFromFormFields(formFields: FormFieldConfig<T>[]): ColumnDef<T>[] {
    const columns: ColumnDef<T>[] = []

    for (const field of formFields) {
      const column: ColumnDef<T> = {
        ...(field.key && { accessorKey: field.key } as any),
        header: field.label,
        enableSorting: !field.required, // 必填字段通常是核心字段，启用排序
        enableHiding: !field.required,
        cell: this.generateCellRenderer(field),
        meta: {
          searchable: true,
          fieldType: field.type,
        } as any,
      }

      // 根据表单字段类型设置列宽
      if (field.type) {
        column.size = this.getColumnWidthByType(field.type)
      }

      // 处理选项字段的渲染
      if (field.options && field.options.length > 0) {
        column.cell = ({ getValue }: any) => {
          const value = getValue()
          const option = field.options!.find(opt => opt.value === value)
          return option ? option.label : value
        }
      }

      columns.push(column)
    }

    // 添加操作列
    columns.push(this.createActionsColumn())

    return columns
  }

  /**
   * 从表格列自动生成表单字段配置
   */
  generateFormFieldsFromColumns(columns: ColumnDef<T>[]): FormFieldConfig<T>[] {
    const formFields: FormFieldConfig<T>[] = []

    for (const column of columns) {
      const meta = column.meta as any
      // 跳过操作列和特殊列
      if (!meta?.isFormField) {
        continue
      }

      const key = this.getColumnKey(column)
      const label = this.getColumnLabel(column)

      if (!key || !label)
        continue

      const field: FormFieldConfig<T> = {
        key: key as keyof T,
        label,
        type: this.inferFormTypeFromColumn(column),
        required: false, // 默认非必填，可通过其他信息推断
        placeholder: `请输入${label}`,
      }

      // 从列的 meta 信息推断更多属性
      if (column.meta) {
        Object.assign(field, meta)
      }

      formFields.push(field)
    }

    return formFields
  }

  /**
   * 智能检测并补全配置
   * 如果只提供了表单配置，自动生成表格配置；反之亦然
   */
  autoCompleteConfig(config: {
    formFields?: FormFieldConfig<T>[]
    columns?: ColumnDef<T>[]
    extraColumns?: (ColumnDef<T> & { order?: number })[]
    columnOrder?: string[]
  }): {
    formFields: FormFieldConfig<T>[]
    columns: ColumnDef<T>[]
  } {
    let { formFields, columns, extraColumns, columnOrder } = config

    // 如果只有表单配置，自动生成表格配置
    if (formFields && formFields.length > 0 && (!columns || columns.length === 0)) {
      columns = this.generateColumnsFromFormFields(formFields)
    }
    // 如果只有表格配置，自动生成表单配置
    else if (columns && columns.length > 0 && (!formFields || formFields.length === 0)) {
      formFields = this.generateFormFieldsFromColumns(columns)
    }
    // 如果都有配置，检测重复字段并优化
    else if (formFields && columns && formFields.length > 0 && columns.length > 0) {
      const optimized = this.optimizeExistingConfig(formFields, columns)
      formFields = optimized.formFields
      columns = optimized.columns
    }

    // 合并额外的列
    if (extraColumns && extraColumns.length > 0) {
      columns = this.mergeExtraColumns(columns || [], extraColumns)
    }

    // 应用列排序
    if (columnOrder && columnOrder.length > 0 && columns) {
      columns = this.sortColumnsByOrder(columns, columnOrder)
    }

    return {
      formFields: formFields || [],
      columns: columns || [],
    }
  }

  /**
   * 优化现有配置，减少重复
   */
  private optimizeExistingConfig(
    formFields: FormFieldConfig<T>[],
    columns: ColumnDef<T>[],
  ): {
    formFields: FormFieldConfig<T>[]
    columns: ColumnDef<T>[]
  } {
    const fieldMap = new Map<string, FormFieldConfig<T>>()
    const columnMap = new Map<string, ColumnDef<T>>()

    // 建立字段映射
    formFields.forEach((field) => {
      if (field.key) {
        fieldMap.set(String(field.key), field)
      }
    })

    columns.forEach((column) => {
      const key = this.getColumnKey(column)
      if (key) {
        columnMap.set(key, column)
      }
    })

    // 检测重复字段并增强配置
    const enhancedColumns = columns.map((column) => {
      const key = this.getColumnKey(column)
      const correspondingField = key ? fieldMap.get(key) : null

      if (correspondingField) {
        // 使用表单字段信息增强表格列
        return this.enhanceColumnWithField(column, correspondingField)
      }

      return column
    })

    const enhancedFormFields = formFields.map((field) => {
      const correspondingColumn = columnMap.get(String(field.key))

      if (correspondingColumn) {
        // 使用表格列信息增强表单字段
        return this.enhanceFieldWithColumn(field, correspondingColumn)
      }

      return field
    })

    return {
      formFields: enhancedFormFields,
      columns: enhancedColumns,
    }
  }

  /**
   * 使用表单字段信息增强表格列
   */
  private enhanceColumnWithField(
    column: ColumnDef<T>,
    field: FormFieldConfig<T>,
  ): ColumnDef<T> {
    const enhanced = { ...column }

    // 如果列没有标题，使用表单字段的标签
    if (!enhanced.header && field.label) {
      enhanced.header = field.label
    }

    // 根据表单字段类型优化单元格渲染
    if (field.type && !enhanced.cell) {
      enhanced.cell = this.generateCellRenderer(field)
    }

    // 根据表单字段的必填属性设置列属性
    if (field.required !== undefined) {
      enhanced.enableHiding = !field.required
      enhanced.meta = {
        ...enhanced.meta,
        required: field.required,
      } as any
    }

    // 处理选项字段
    if (field.options && field.options.length > 0) {
      enhanced.cell = ({ getValue }: any) => {
        const value = getValue()
        const option = field.options!.find(opt => opt.value === value)
        return option ? option.label : value
      }
    }

    return enhanced
  }

  /**
   * 使用表格列信息增强表单字段
   */
  private enhanceFieldWithColumn(
    field: FormFieldConfig<T>,
    column: ColumnDef<T>,
  ): FormFieldConfig<T> {
    const enhanced = { ...field }

    // 如果字段没有标签，使用表格列的标题
    if (!enhanced.label) {
      const columnLabel = this.getColumnLabel(column)
      if (columnLabel) {
        enhanced.label = columnLabel
      }
    }

    // 从列的 meta 信息推断表单属性
    if (column.meta) {
      const meta = column.meta as any
      if (meta.required !== undefined && enhanced.required === undefined) {
        enhanced.required = meta.required
      }
    }

    return enhanced
  }

  /**
   * 根据表单字段生成单元格渲染器
   */
  private generateCellRenderer(field: FormFieldConfig<T>) {
    switch (field.type) {
      case 'switch':
      case 'checkbox':
        return ({ getValue }: any) => getValue() ? '是' : '否'

      case 'date':
        return ({ getValue }: any) => {
          const value = getValue()
          return value ? new Date(value).toLocaleDateString() : ''
        }

      case 'datetime':
        return ({ getValue }: any) => {
          const value = getValue()
          return value ? new Date(value).toLocaleString() : ''
        }

      case 'email':
        return ({ getValue }: any) => {
          const value = getValue()
          return value || ''
        }

      case 'select':
      case 'radio':
        if (field.options && field.options.length > 0) {
          return ({ getValue }: any) => {
            const value = getValue()
            const option = field.options!.find(opt => opt.value === value)
            return option ? option.label : value
          }
        }
        break

      case 'textarea':
        return ({ getValue }: any) => {
          const value = getValue()
          if (!value)
            return ''
          // 长文本截断显示
          return value.length > 50 ? `${value.slice(0, 50)}...` : value
        }
    }

    // 默认渲染器
    return ({ getValue }: any) => getValue() || ''
  }

  /**
   * 根据表单字段类型推断列宽
   */
  private getColumnWidthByType(type: FormFieldType): number {
    switch (type) {
      case 'checkbox':
      case 'switch':
        return 80
      case 'number':
        return 100
      case 'date':
        return 120
      case 'datetime':
        return 160
      case 'email':
        return 200
      case 'textarea':
        return 250
      case 'select':
      case 'radio':
        return 120
      default:
        return 150
    }
  }

  /**
   * 从表格列推断表单字段类型
   */
  private inferFormTypeFromColumn(column: ColumnDef<T>): FormFieldType {
    // 从列的 meta 信息获取
    if (column.meta) {
      const meta = column.meta as any
      if (meta.fieldType) {
        return meta.fieldType
      }
    }

    // 根据列名推断
    const key = this.getColumnKey(column)
    if (key) {
      const keyLower = key.toLowerCase()

      if (keyLower.includes('email'))
        return 'email'
      if (keyLower.includes('password'))
        return 'password'
      if (keyLower.includes('phone') || keyLower.includes('mobile'))
        return 'text'
      if (keyLower.includes('date')) {
        return keyLower.includes('time') ? 'datetime' : 'date'
      }
      if (keyLower.includes('status') || keyLower.includes('enabled'))
        return 'switch'
      if (keyLower.includes('description') || keyLower.includes('content'))
        return 'textarea'
      if (keyLower.includes('count') || keyLower.includes('amount'))
        return 'number'
    }

    return 'text'
  }

  /**
   * 获取列的键名
   */
  private getColumnKey(column: ColumnDef<T>): string | null {
    return (column as any).accessorKey || column.id || null
  }

  /**
   * 获取列的标签
   */
  private getColumnLabel(column: ColumnDef<T>): string | null {
    if (typeof column.header === 'string') {
      return column.header
    }
    return null
  }

  /**
   * 判断是否为数据列（非操作列等特殊列）
   */
  private isDataColumn(column: ColumnDef<T>): boolean {
    return column.id !== 'actions'
      && (column as any).accessorKey !== undefined
      && !(column.meta as any)?.isFormField
  }

  /**
   * 创建操作列
   */
  private createActionsColumn(): ColumnDef<T> {
    return {
      id: 'actions',
      header: '操作',
      size: 300,
      enableSorting: false,
      enableHiding: false,
      meta: {
        fixed: 'right',
      } as any,
    } as ColumnDef<T>
  }

  /**
   * 合并额外的列到主列中
   */
  private mergeExtraColumns(existingColumns: ColumnDef<T>[], extraColumns: (ColumnDef<T> & { order?: number })[]): ColumnDef<T>[] {
    const result: ColumnDef<T>[] = []
    const actionsColumnIndex = existingColumns.findIndex(col => col.id === 'actions')

    // 为现有列添加默认 order
    const columnsWithOrder = existingColumns
      .filter(col => col.id !== 'actions')
      .map((col, index) => ({ ...col, order: index * 10 })) // 给现有列留出插入空间

    // 合并额外的列
    extraColumns.forEach((extraColumn) => {
      const { order = 999, ...columnDef } = extraColumn
      // 检查是否已存在具有相同键名的列
      const existingColumnIndex = columnsWithOrder.findIndex(col => this.getColumnKey(col) === this.getColumnKey(columnDef))
      if (existingColumnIndex >= 0) {
        // 如果存在，则合并配置（extraColumn 优先级更高）
        columnsWithOrder[existingColumnIndex] = { ...columnsWithOrder[existingColumnIndex], ...columnDef, order }
      }
      else {
        // 如果不存在，则直接添加
        columnsWithOrder.push({ ...columnDef, order })
      }
    })

    // 按 order 排序
    columnsWithOrder.sort((a, b) => (a.order || 999) - (b.order || 999))

    // 移除 order 属性并添加到结果
    result.push(...columnsWithOrder.map(({ order, ...col }) => col))

    // 最后添加操作列（如果存在）
    if (actionsColumnIndex >= 0) {
      result.push(existingColumns[actionsColumnIndex])
    }

    return result
  }

  /**
   * 根据指定的顺序排列列
   */
  private sortColumnsByOrder(columns: ColumnDef<T>[], columnOrder: string[]): ColumnDef<T>[] {
    const result: ColumnDef<T>[] = []
    const columnMap = new Map<string, ColumnDef<T>>()
    const actionsColumn = columns.find(col => col.id === 'actions')

    // 建立列的映射关系
    columns.forEach((col) => {
      if (col.id !== 'actions') {
        const key = this.getColumnKey(col)
        if (key) {
          columnMap.set(key, col)
        }
      }
    })

    // 按指定顺序添加列
    columnOrder.forEach((key) => {
      if (columnMap.has(key)) {
        result.push(columnMap.get(key)!)
        columnMap.delete(key)
      }
    })

    // 添加未在 columnOrder 中指定的列
    columnMap.forEach((col) => {
      result.push(col)
    })

    // 最后添加操作列
    if (actionsColumn) {
      result.push(actionsColumn)
    }

    return result
  }
}

/**
 * 创建字段复用检测器实例
 */
export function createFieldReuseDetector<T extends Record<string, any> = any>(): FieldReuseDetector<T> {
  return new FieldReuseDetector<T>()
}
