# @billing/curd - 现代化 Vue 3 CURD 组件库

一个基于 Vue 3 + TypeScript + TanStack Table 的现代化 CURD 组件库，内置自动字段复用功能，大幅减少配置代码量。

## ✨ 核心特性

- 🔄 **自动字段复用** - 只配置一种字段类型，另一种自动生成，减少 40-60% 的代码量
- 📉 **智能推断** - 根据字段类型和名称自动生成最佳配置
- 🚀 **零学习成本** - 保持原有 API，内部自动优化
- 🎯 **一键 CURD** - 3 行代码即可拥有完整的增删改查系统
- 💪 **类型安全** - 完整的 TypeScript 支持
- 🔌 **高度可扩展** - 支持自定义组件和渲染器
- 🎨 **自定义渲染** - 完全自定义的列和表头渲染

## 🚀 快速开始

### 安装

```bash
pnpm add @billing/curd
```

### 5分钟上手 - 最简使用

只需要 **3 行代码**，即可拥有完整的增删改查系统：

```vue
<script setup lang="ts">
import { createCosyApi, EasyCurd } from '@billing/curd'

// 1. 创建 API 实例
const userApi = createCosyApi({
  baseURL: '/api',
  resource: 'users'
})

// 2. 最简配置
const config = {
  api: userApi,
  title: '用户管理'
}
</script>

<template>
  <!-- 3. 一键完成 CURD -->
  <EasyCurd :config="config" />
</template>
```

**就是这么简单！** 你现在已经拥有了：
- 📋 数据表格（自动推断列）
- ➕ 新建用户（自动生成表单）
- ✏️ 编辑用户（自动填充数据）
- 🗑️ 删除用户（安全确认）
- 🔍 搜索功能
- 📄 分页功能

### 功能开关

```vue
<script setup lang="ts">
const config = {
  api: userApi,
  title: '用户管理',
  features: {
    create: true, // 显示"新建"按钮
    edit: true, // 显示"编辑"按钮
    delete: true, // 显示"删除"按钮
    batchDelete: true, // 显示"批量删除"按钮
    search: true, // 显示搜索框
    export: false, // 隐藏"导出"按钮
  }
}
</script>
```

### 自定义消息

```vue
<script setup lang="ts">
const config = {
  api: userApi,
  title: '用户管理',

  // 确认消息
  confirmMessages: {
    delete: '确定要删除这个用户吗？删除后无法恢复！',
    batchDelete: '确定要删除选中的用户吗？此操作不可撤销！'
  },

  // 成功消息
  successMessages: {
    create: '用户创建成功！',
    update: '用户信息更新成功！',
    delete: '用户删除成功！',
    batchDelete: '批量删除完成！'
  }
}
</script>
```

## 🔄 自动字段复用功能

### 核心优势

✨ **零配置** - 无需学习新的API，保持原有使用方式
🔄 **自动转换** - 只配置一种字段类型，另一种自动生成
🚀 **智能优化** - 检测重复字段并自动增强配置
📉 **减少代码量** - 平均减少 40-60% 的配置代码

### 四种使用方式

#### 1. 只配置表单，自动生成表格

```typescript
const config = {
  api: userApi,
  formFields: [
    {
      key: 'name',
      label: '用户名',
      type: 'text',
      required: true,
    },
    {
      key: 'status',
      label: '状态',
      type: 'switch',
      defaultValue: true,
    },
  ],
  // 表格列配置将自动生成：
  // - name: 文本列，启用排序，宽度150px
  // - status: 是/否显示，居中对齐，宽度80px
  // - actions: 操作列，固定右侧，宽度120px
}
```

#### 2. 只配置表格，自动生成表单

```typescript
const config = {
  api: userApi,
  columns: [
    {
      accessorKey: 'name',
      header: '用户名',
      size: 120,
      meta: { fieldType: 'text' }, // 提示表单字段类型
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ getValue }) => getValue() ? '启用' : '禁用',
      meta: { fieldType: 'switch' }, // 提示表单字段类型
    },
  ],
  // 表单字段配置将自动生成：
  // - name: 文本输入框，占位符"请输入用户名"
  // - status: 开关组件，默认非必填
}
```

#### 3. 配置两种，自动优化

```typescript
const config = {
  api: userApi,
  formFields: [
    {
      key: 'name',
      label: '用户名称', // 这个标签会应用到表格
      type: 'text',
      required: true, // 这个属性会影响表格列
    },
  ],
  columns: [
    {
      accessorKey: 'name',
      // header 未设置，会使用表单的 label: '用户名称'
      size: 120,
      // cell 未设置，会根据表单的 type: 'text' 自动生成
    },
  ],
  // 系统会自动检测重复字段并相互增强配置
}
```

#### 4. 表单生成表格 + 额外表格列

```typescript
const config = {
  api: userApi,
  formFields: [
    {
      key: 'name',
      label: '用户名',
      type: 'text',
      required: true,
    },
    {
      key: 'email',
      label: '邮箱',
      type: 'email',
      required: true,
    },
  ],
  // 额外的表格列（不参与表单编辑）
  extraColumns: [
    {
      accessorKey: 'created_at',
      header: '创建时间',
      cell: ({ getValue }) => new Date(getValue()).toLocaleDateString(),
      size: 120,
    },
    {
      accessorKey: 'last_login_at',
      header: '最后登录',
      cell: ({ getValue }) => getValue() ? new Date(getValue()).toLocaleString() : '从未登录',
      size: 160,
    },
    {
      id: 'user_stats',
      header: '统计信息',
      cell: ({ row }) => `登录 ${row.original.login_count} 次`,
      size: 100,
    },
  ],
  // 最终会生成包含表单字段和额外列的完整表格
  // - name: 文本列（可编辑）
  // - email: 邮箱列（可编辑）
  // - created_at: 创建时间（只读）
  // - last_login_at: 最后登录（只读）
  // - user_stats: 统计信息（只读）
  // - actions: 操作列
}
```

## 🔀 列排序控制

### 方式一：使用 order 属性

为 `extraColumns` 中的列添加 `order` 属性来控制排序：

```typescript
const config = {
  api: userApi,
  formFields: [
    { key: 'name', label: '姓名', type: 'text' }, // order: 0 (默认)
    { key: 'email', label: '邮箱', type: 'email' }, // order: 10 (默认)
  ],
  extraColumns: [
    {
      accessorKey: 'created_at',
      header: '创建时间',
      cell: ({ getValue }) => formatDate(getValue()),
      order: 5, // 插入在 name 和 email 之间
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ getValue }) => getValue() ? '活跃' : '禁用',
      order: 15, // 排在 email 之后
    },
    {
      id: 'stats',
      header: '统计',
      cell: ({ row }) => `${row.original.login_count} 次登录`,
      order: 25, // 排在最后
    },
  ],
}

// 最终列顺序：姓名 → 创建时间 → 邮箱 → 状态 → 统计 → 操作
```

### 方式二：使用 columnOrder 全局排序

通过 `columnOrder` 数组精确控制所有列的顺序：

```typescript
const config = {
  api: userApi,
  formFields: [
    { key: 'name', label: '姓名', type: 'text' },
    { key: 'email', label: '邮箱', type: 'email' },
    { key: 'phone', label: '电话', type: 'text' },
  ],
  extraColumns: [
    {
      accessorKey: 'created_at',
      header: '创建时间',
      cell: ({ getValue }) => formatDate(getValue()),
    },
    {
      accessorKey: 'department.name',
      header: '部门',
      cell: ({ getValue }) => getValue() || '未分配',
    },
  ],
  // 全局列顺序配置
  columnOrder: [
    'name', // 姓名放第一
    'department.name', // 部门放第二
    'email', // 邮箱放第三
    'created_at', // 创建时间放第四
    'phone', // 电话放第五
    // actions 列会自动放在最后
  ],
}

// 最终列顺序严格按照 columnOrder 数组
```

### 方式三：混合使用

```typescript
const config = {
  api: userApi,
  formFields: [
    { key: 'name', label: '姓名', type: 'text' },
    { key: 'email', label: '邮箱', type: 'email' },
  ],
  extraColumns: [
    {
      accessorKey: 'created_at',
      header: '创建时间',
      order: 5, // 使用 order 控制
    },
    {
      accessorKey: 'status',
      header: '状态',
      order: 15,
    },
  ],
  // columnOrder 会覆盖 order 属性
  columnOrder: ['status', 'name', 'email', 'created_at'],
}
```

### 智能推断规则

#### 表单类型转表格渲染

| 表单类型 | 表格显示 | 列宽 |
|---------|---------|------|
| `text/number/email` | 直接显示值 | 150px |
| `switch/checkbox` | 显示"是/否" | 80px |
| `select/radio` | 显示选项 label | 120px |
| `date` | 本地日期格式 | 120px |
| `datetime` | 本地日期时间 | 160px |
| `textarea` | 截断长文本 | 250px |

#### 字段名推断表单类型

| 字段名包含 | 推断类型 |
|-----------|---------|
| `email` | `email` |
| `password` | `password` |
| `phone/mobile` | `text` |
| `date` | `date` |
| `datetime` | `datetime` |
| `status/enabled` | `switch` |
| `description/content` | `textarea` |

## 📝 支持的字段类型

| 类型 | 描述 | 示例 |
|------|------|------|
| `text` | 文本输入框 | 姓名、标题 |
| `number` | 数字输入框 | 年龄、价格 |
| `email` | 邮箱输入框 | 用户邮箱 |
| `password` | 密码输入框 | 登录密码 |
| `textarea` | 多行文本框 | 描述、备注 |
| `select` | 下拉选择框 | 部门、分类 |
| `multiselect` | 多选下拉框 | 标签、权限 |
| `checkbox` | 复选框 | 多选项 |
| `radio` | 单选框 | 单选项 |
| `switch` | 开关 | 启用/禁用 |
| `date` | 日期选择器 | 生日、入职日期 |
| `datetime` | 日期时间选择器 | 创建时间 |
| `time` | 时间选择器 | 时间设置 |
| `file` | 文件上传 | 文档上传 |
| `image` | 图片上传 | 头像上传 |

## 📝 自定义表单配置

```vue
<script setup lang="ts">
const config = {
  api: userApi,
  title: '用户管理',
  formFields: [
    {
      key: 'name',
      label: '姓名',
      type: 'text',
      required: true,
      placeholder: '请输入用户姓名',
      rules: [
        { required: true, message: '姓名是必填项' },
        { min: 2, max: 20, message: '姓名长度应在2-20个字符之间' }
      ]
    },
    {
      key: 'email',
      label: '邮箱',
      type: 'email',
      required: true,
      placeholder: '请输入邮箱地址'
    },
    {
      key: 'department',
      label: '部门',
      type: 'select',
      required: true,
      options: [
        { label: '技术部', value: 'tech' },
        { label: '产品部', value: 'product' },
        { label: '运营部', value: 'operation' }
      ]
    },
    {
      key: 'status',
      label: '状态',
      type: 'switch',
      defaultValue: true
    }
  ]
}
</script>
```

## 🎨 自定义渲染功能

CurdTable 和 EasyCurd 组件现在支持完全自定义的列和表头渲染，让您可以根据业务需求灵活定制表格的显示效果。

### 功能特性

- ✅ **表头自定义渲染** - 为任意列自定义表头显示
- ✅ **单元格自定义渲染** - 为任意列自定义单元格内容
- ✅ **操作列自定义** - 完全自定义操作按钮
- ✅ **工具栏自定义** - 自定义工具栏左右两侧内容
- ✅ **完整的插槽参数** - 提供丰富的上下文数据
- ✅ **类型安全** - 完整的 TypeScript 类型支持

### 插槽系统

#### 1. 表头自定义渲染

**插槽名格式：** `header-{columnId}`

```vue
<template>
  <EasyCurd :config="config">
    <!-- 为 name 列自定义表头 -->
    <template #header-name="{ header, column, context }">
      <div class="flex items-center gap-2">
        <User class="w-4 h-4" />
        用户名
      </div>
    </template>

    <!-- 为 status 列添加说明 -->
    <template #header-status>
      <div class="text-center">
        <div>状态</div>
        <div class="text-xs text-muted-foreground">
          (启用/禁用)
        </div>
      </div>
    </template>
  </EasyCurd>
</template>
```

#### 2. 单元格自定义渲染

**插槽名格式：** `cell-{columnId}`

```vue
<template>
  <EasyCurd :config="config">
    <!-- 用户名显示头像 -->
    <template #cell-name="{ value, row }">
      <div class="flex items-center gap-3">
        <Avatar class="w-8 h-8">
          <AvatarImage :src="row.avatar" />
          <AvatarFallback>{{ value?.charAt(0)?.toUpperCase() }}</AvatarFallback>
        </Avatar>
        <div>
          <div class="font-medium">
            {{ value }}
          </div>
          <div class="text-sm text-muted-foreground">
            {{ row.email }}
          </div>
        </div>
      </div>
    </template>

    <!-- 状态显示徽章 -->
    <template #cell-status="{ value }">
      <Badge :variant="value ? 'default' : 'secondary'">
        <div class="flex items-center gap-1">
          <div
            class="w-2 h-2 rounded-full"
            :class="value ? 'bg-green-500' : 'bg-gray-400'"
          />
          {{ value ? '启用' : '禁用' }}
        </div>
      </Badge>
    </template>

    <!-- 时间格式化 -->
    <template #cell-created_at="{ value }">
      <div class="text-sm">
        <div>{{ formatDate(value) }}</div>
        <div class="text-muted-foreground">
          {{ formatTimeAgo(value) }}
        </div>
      </div>
    </template>
  </EasyCurd>
</template>
```

#### 3. 操作列自定义

**插槽名：** `actions`

```vue
<template>
  <EasyCurd :config="config">
    <!-- 完全自定义操作列 -->
    <template #actions="{ row }">
      <div class="flex items-center justify-end gap-1">
        <Button
          variant="ghost"
          size="sm"
          @click="viewUser(row)"
        >
          <Eye class="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="editUser(row)"
        >
          <Edit class="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          @click="resetPassword(row)"
        >
          <Key class="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          class="text-destructive"
          @click="deleteUser(row)"
        >
          <Trash2 class="w-4 h-4" />
        </Button>
      </div>
    </template>
  </EasyCurd>
</template>
```

#### 4. 工具栏自定义

**插槽名：** `toolbar-left` | `toolbar-right`

```vue
<template>
  <EasyCurd :config="config">
    <!-- 自定义工具栏左侧 -->
    <template #toolbar-left>
      <Button
        size="sm"
        @click="exportData"
      >
        <Download class="w-4 h-4 mr-2" />
        导出数据
      </Button>
      <Button
        variant="outline"
        size="sm"
        @click="importData"
      >
        <Upload class="w-4 h-4 mr-2" />
        导入数据
      </Button>
    </template>

    <!-- 自定义工具栏右侧 -->
    <template #toolbar-right>
      <div class="flex items-center gap-2">
        <Input
          v-model="searchKeyword"
          placeholder="搜索..."
          class="w-48"
        />
        <Select v-model="statusFilter">
          <SelectTrigger class="w-32">
            <SelectValue placeholder="状态筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">
              全部
            </SelectItem>
            <SelectItem value="active">
              启用
            </SelectItem>
            <SelectItem value="inactive">
              禁用
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </template>
  </EasyCurd>
</template>
```

### 常用渲染模式

#### 图片显示

```vue
<template #cell-avatar="{ value, row }">
  <Avatar class="w-10 h-10">
    <AvatarImage
      :src="value"
      :alt="row.name"
    />
    <AvatarFallback>{{ row.name?.charAt(0) }}</AvatarFallback>
  </Avatar>
</template>
```

#### 状态指示器

```vue
<template #cell-status="{ value }">
  <div class="flex items-center gap-2">
    <div
      class="w-2 h-2 rounded-full"
      :class="{
        'bg-green-500': value === 'active',
        'bg-red-500': value === 'inactive',
        'bg-yellow-500': value === 'pending',
      }"
    />
    <span class="capitalize">{{ value }}</span>
  </div>
</template>
```

#### 进度条

```vue
<template #cell-progress="{ value }">
  <div class="w-full">
    <div class="flex justify-between text-sm mb-1">
      <span>进度</span>
      <span>{{ value }}%</span>
    </div>
    <Progress
      :value="value"
      class="h-2"
    />
  </div>
</template>
```

#### 标签组合

```vue
<template #cell-tags="{ value }">
  <div class="flex flex-wrap gap-1">
    <Badge
      v-for="tag in value"
      :key="tag"
      variant="secondary"
      class="text-xs"
    >
      {{ tag }}
    </Badge>
  </div>
</template>
```

## 🔧 高级功能

### 条件字段显示
```javascript
{
  key: 'vip_price',
  label: 'VIP价格',
  type: 'number',
  show: (formData) => formData.category === 'vip' // 只在VIP分类时显示
}
```

### 动态禁用字段
```javascript
{
  key: 'code',
  label: '产品编码',
  type: 'text',
  disabled: (formData) => formData.id !== undefined // 编辑时禁用
}
```

### 自定义验证器
```javascript
{
  key: 'username',
  label: '用户名',
  type: 'text',
  rules: [
    {
      validator: (value, formData) => {
        if (value.includes('admin')) {
          return '用户名不能包含admin'
        }
        return true
      }
    }
  ]
}
```

### 生命周期钩子
```javascript
const config = {
  api: userApi,
  title: '用户管理',
  hooks: {
    beforeCreate: async (data) => {
      // 创建前处理
      return { ...data, createdBy: 'admin' }
    },
    afterCreate: async (result) => {
      console.log('创建成功:', result)
    },
    onError: async (error, operation) => {
      console.error(`操作失败:`, error)
    }
  }
}
```

## 🌟 完整示例

以下是一个完整的产品管理系统示例：

```vue
<script setup lang="ts">
import { createCosyApi, EasyCurd } from '@billing/curd'

interface Product {
  id: number
  name: string
  description: string
  price: number
  category: string
  status: boolean
  created_at: string
}

const productApi = createCosyApi<Product>({
  baseURL: '/api',
  resource: 'products'
})

const config = {
  api: productApi,
  title: '产品管理',

  features: {
    create: true,
    edit: true,
    delete: true,
    batchDelete: true,
    search: true
  },

  formFields: [
    {
      key: 'name',
      label: '产品名称',
      type: 'text',
      required: true,
      rules: [
        { required: true, message: '产品名称必填' },
        { min: 2, max: 50, message: '长度在2-50个字符' }
      ]
    },
    {
      key: 'description',
      label: '产品描述',
      type: 'textarea',
      placeholder: '请输入产品详细描述'
    },
    {
      key: 'price',
      label: '价格',
      type: 'number',
      required: true,
      rules: [
        { required: true, message: '价格必填' },
        { min: 0, message: '价格不能为负数' }
      ]
    },
    {
      key: 'category',
      label: '分类',
      type: 'select',
      required: true,
      options: [
        { label: '电子产品', value: 'electronics' },
        { label: '服装', value: 'clothing' },
        { label: '家居', value: 'home' },
        { label: '图书', value: 'books' }
      ]
    },
    {
      key: 'status',
      label: '上架状态',
      type: 'switch',
      defaultValue: true
    }
  ],

  confirmMessages: {
    delete: '确定要删除这个产品吗？删除后将无法恢复！',
    batchDelete: '确定要删除选中的产品吗？'
  },

  successMessages: {
    create: '产品创建成功！',
    update: '产品更新成功！',
    delete: '产品删除成功！'
  }
}
</script>

<template>
  <div class="p-6">
    <EasyCurd :config="config" />
  </div>
</template>
```

## 📚 API 参考

### EasyCurdConfig

```typescript
interface EasyCurdConfig<T> {
  api: CurdApi<T> // API 配置
  title?: string // 页面标题

  // 字段配置（二选一或两者都配置）
  formFields?: FormFieldConfig<T>[] // 表单字段
  columns?: ColumnDef<T>[] // 表格列
  extraColumns?: ExtendedColumn<T>[] // 额外的表格列（不参与表单）
  columnOrder?: string[] // 全局列顺序配置

  // 基础配置
  primaryKey?: string // 主键字段名
  pageSize?: number // 每页条数
  pageSizeOptions?: number[] // 分页选项

  // 功能开关
  features?: {
    create?: boolean // 是否支持创建
    edit?: boolean // 是否支持编辑
    delete?: boolean // 是否支持删除
    batchDelete?: boolean // 是否支持批量删除
    search?: boolean // 是否支持搜索
  }

  // 消息配置
  confirmMessages?: {
    delete?: string
    batchDelete?: string
  }

  hooks?: CurdHooks<T> // 钩子函数
}
```

### FormFieldConfig

```typescript
interface FormFieldConfig<T> {
  key: keyof T // 字段名
  label: string // 显示标签
  type?: FormFieldType // 字段类型
  required?: boolean // 是否必填
  placeholder?: string // 占位符
  defaultValue?: any // 默认值
  rules?: ValidationRule[] // 验证规则
  options?: SelectOption[] // 选项（select/radio）
  component?: any // 自定义组件
  show?: boolean | ((formData: Partial<T>) => boolean) // 条件显示
  disabled?: boolean | ((formData: Partial<T>) => boolean) // 条件禁用
}
```

### CurdApi

```typescript
interface CurdApi<T> {
  getList: (params?: ListParams) => Promise<ListResponse<T>>
  getItem: (id: string | number) => Promise<T>
  createItem: (data: Partial<T>) => Promise<T>
  updateItem: (id: string | number, data: Partial<T>) => Promise<T>
  deleteItem: (id: string | number, permanently?: boolean) => Promise<void>
  restoreItem: (id: string | number) => Promise<void>
}
```

### CurdHooks

```typescript
interface CurdHooks<T> {
  // 列表相关
  beforeList?: (params: ListParams) => ListParams | Promise<ListParams> | false
  afterList?: (response: ListResponse<T>) => void | Promise<void> | false

  // 创建相关
  beforeCreate?: (data: Partial<T>) => Partial<T> | Promise<Partial<T>> | false
  afterCreate?: (data: T) => void | Promise<void> | false

  // 更新相关
  beforeUpdate?: (id: string | number, data: Partial<T>) => Partial<T> | Promise<Partial<T>> | false
  afterUpdate?: (data: T) => void | Promise<void> | false

  // 删除相关
  beforeDelete?: (id: string | number) => void | Promise<void> | false
  afterDelete?: (id: string | number) => void | Promise<void> | false

  // 表单相关
  beforeFormSubmit?: (formData: Record<string, any>, mode: 'create' | 'update') => Record<string, any> | Promise<Record<string, any>> | false
  afterFormSubmit?: (result: T, mode: 'create' | 'update') => void | Promise<void> | false

  // 错误处理
  onError?: (error: Error, operation: string, context?: any) => void | Promise<void>
}
```

### createCosyApi

```typescript
interface CreateApiOptions {
  baseURL?: string // API 基础地址
  axios?: AxiosInstance // 自定义 axios 实例
  resource: string // 资源名称
}

function createCosyApi<T>(options: CreateApiOptions): CurdApi<T>
```

## 📊 自定义渲染插槽参数

### 表头插槽参数

```typescript
interface HeaderSlotProps {
  header: any // TanStack Table 的 Header 对象
  column: any // TanStack Table 的 Column 对象
  context: any // Header 上下文
}
```

### 单元格插槽参数

```typescript
interface CellSlotProps<T> {
  cell: any // TanStack Table 的 Cell 对象
  row: T // 当前行的完整数据
  value: any // 当前单元格的值
  index: number // 行索引
  column: any // TanStack Table 的 Column 对象
  context: any // Cell 上下文
}
```

### 操作列插槽参数

```typescript
interface ActionsSlotProps<T> {
  row: T // 当前行的完整数据
  index: number // 行索引
}
```

## 🎯 最佳实践

### 1. 渐进式开发

1. **先用智能推断**：让 EasyCurd 自动推断，然后根据需要调整
2. **渐进式增强**：从最简配置开始，逐步添加自定义
3. **合理的字段分组**：相关字段放在一起
4. **清晰的标签和提示**：提供友好的用户体验

### 2. 何时使用 extraColumns

使用 `extraColumns` 的场景：

- **只读字段**：创建时间、更新时间、系统状态等
- **计算字段**：基于其他字段计算得出的值
- **关联字段**：来自其他表的关联数据
- **统计信息**：登录次数、操作记录等
- **格式化显示**：需要特殊格式化但不需要编辑的字段

```typescript
const config = {
  api: userApi,
  formFields: [
    // 可编辑的基础字段
    { key: 'name', label: '姓名', type: 'text', required: true },
    { key: 'email', label: '邮箱', type: 'email', required: true },
  ],
  extraColumns: [
    // 只读的系统字段
    {
      accessorKey: 'created_at',
      header: '注册时间',
      cell: ({ getValue }) => formatDate(getValue()),
    },
    // 计算字段
    {
      id: 'full_info',
      header: '完整信息',
      cell: ({ row }) => `${row.original.name} (${row.original.email})`,
    },
    // 关联数据
    {
      accessorKey: 'department.name',
      header: '部门',
      cell: ({ getValue }) => getValue() || '未分配',
    },
  ],
}
```

### 3. 性能优化

- 避免在模板中进行复杂计算，使用计算属性
- 为循环渲染的元素提供唯一的 `key`
- 使用 `v-memo` 优化静态内容

```vue
<template #cell-roles="{ value }">
  <div class="flex flex-wrap gap-1">
    <Badge
      v-for="role in value"
      :key="`${role.id}-${role.name}`"
      v-memo="[role.id, role.name]"
      variant="outline"
    >
      {{ role.name }}
    </Badge>
  </div>
</template>
```

### 4. 响应式设计

考虑不同屏幕尺寸的显示效果：

```vue
<template #cell-description="{ value }">
  <div class="max-w-xs truncate md:max-w-sm lg:max-w-md">
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger class="text-left">
          {{ value }}
        </TooltipTrigger>
        <TooltipContent>
          <p class="max-w-xs">
            {{ value }}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
</template>
```

### 5. 无障碍支持

为操作按钮提供明确的标签：

```vue
<template #actions="{ row }">
  <div class="flex items-center gap-1">
    <Button
      variant="ghost"
      size="sm"
      :aria-label="`编辑 ${row.name}`"
      @click="editUser(row)"
    >
      <Edit class="w-4 h-4" />
      <span class="sr-only">编辑</span>
    </Button>
  </div>
</template>
```

## 🤔 常见问题

**Q: 如何处理文件上传字段？**
A: 使用 `file` 或 `image` 类型，配合自定义组件处理上传逻辑。

**Q: 如何处理关联字段（外键）？**
A: 使用 `select` 类型，通过 `options` 提供选择项，或使用自定义组件。

**Q: 如何自定义操作按钮？**
A: 可以通过插槽自定义操作列，或使用 CurdTable 组件获得更多控制。

**Q: 如何处理复杂的数据转换？**
A: 使用生命周期钩子（hooks）在数据提交前后进行处理。

**Q: 如何实现条件字段显示？**
A: 使用字段配置的 `show` 属性，支持函数形式根据表单数据动态控制。

**Q: 如何处理表单验证？**
A: 通过 `rules` 配置验证规则，支持内置验证和自定义验证器。

**Q: 如何添加表格专有的列（不参与表单编辑）？**
A: 使用 `extraColumns` 配置额外的表格列，这些列只在表格中显示，不会出现在新建/编辑表单中。

**Q: extraColumns 和 columns 有什么区别？**
A: `columns` 是完整的表格列定义，会覆盖自动生成的列；`extraColumns` 是在自动生成列的基础上追加的额外列，通常用于只读字段。

**Q: 如何控制表格列的显示顺序？**
A: 有两种方式：1) 为 `extraColumns` 中的列添加 `order` 属性；2) 使用 `columnOrder` 数组全局控制所有列的顺序。

**Q: order 属性和 columnOrder 配置有什么区别？**
A: `order` 属性用于单个列的相对排序，数字越小越靠前；`columnOrder` 是全局配置，会覆盖 `order` 属性，精确控制所有列的顺序。

**Q: 操作列（actions）的位置可以控制吗？**
A: 操作列始终显示在最后，无法通过 `order` 或 `columnOrder` 控制其位置，这是为了保持操作的一致性。

## 🔧 开发和贡献

### 本地开发

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 运行测试
pnpm test

# 构建
pnpm build
```

### 项目结构

```
src/
├── api/                    # API 相关
├── components/             # 组件
│   ├── CurdForm.vue       # 表单组件
│   ├── CurdTable.vue      # 表格组件
│   ├── CurdPagination.vue # 分页组件
│   └── EasyCurd.vue       # 主组件
├── composables/           # 组合式函数
│   └── useCurd.ts        # 核心逻辑
├── types/                 # 类型定义
├── utils/                 # 工具函数
│   └── field-reuse-detector.ts  # 字段复用检测器
└── index.ts              # 入口文件
```

## 📄 许可证

MIT License

---

**开始构建你的第一个一键 CURD 系统吧！🎉**
