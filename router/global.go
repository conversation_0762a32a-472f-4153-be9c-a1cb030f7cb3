package router

import (
	"git.uozi.org/uozi/potato-billing-api/api/global/auth"
	"git.uozi.org/uozi/potato-billing-api/api/global/upload"
	"git.uozi.org/uozi/potato-billing-api/api/global/user"
	"github.com/uozi-tech/cosy"
)

func initGlobalRouter() {
	r := cosy.GetEngine()
	auth.InitRouter(r)
	a := r.Group("/", AuthRequired())
	{
		user.InitRouter(a)
		upload.InitRouter(a)
		auth.InitAuthRouter(a)
	}
}
