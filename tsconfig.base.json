{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "composite": true,
    "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": [
      "ESNext",
      "DOM",
      "DOM.Iterable"
    ],
    "noEmit": false,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "allowJs": false,
    "noImplicitAny": false,

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "esModuleInterop": true,
    "skipLibCheck": true,

    "baseUrl": ".",
    "paths": {
      "@ui/*": [
        "./packages/ui/src/*"
      ],
      "@curd/*": [
        "./packages/curd/src/*"
      ]
    }
  },
  "exclude": ["node_modules", "dist", "eslint.config.mjs", "packages/ui"]
}
