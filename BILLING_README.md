# 计费系统实现说明

基于Go 1.23+、MQTT、GORM构建的实时计费系统，支持AI服务用量上报和Key状态管理。

## 🚀 功能特性

### ✅ 已实现功能

1. **数据库模型** (`model/billing_key.go`)
   - ✅ BillingKey: Key管理表
   - ✅ BillingKeyQuota: Key配额表（支持多模块）
   - ✅ BillingUsageLog: 用量日志表
   - ✅ BillingPricingRule: 价格规则表

2. **MQTT消息处理** (`internal/billing/mqtt_service.go`)
   - ✅ 订阅用量上报主题 `usage-report`
   - ✅ 解析和验证用量消息
   - ✅ 记录用量到数据库
   - ✅ 自动更新配额使用量
   - ✅ Key状态检查和更新
   - ✅ Key状态推送 `key-status/{key}`

3. **HTTP API服务** (`api/billing/billing.go`)
   - ✅ `GET /api/billing/keys/{key}` - 获取Key状态
   - ✅ `POST /api/billing/keys/{key}/quota` - 更新Key配额
   - ✅ `GET /api/billing/keys/{key}/usage` - 获取用量历史
   - ✅ `GET /api/billing/keys/{key}/stats` - 获取用量统计
   - ✅ `GET /api/billing/health` - 健康检查

4. **Redis缓存** (`internal/billing/cache_service.go`)
   - ✅ Key状态缓存
   - ✅ 配额使用量缓存
   - ✅ 价格规则缓存
   - ✅ 缓存失效和更新

5. **服务整合** (`internal/billing/billing_service.go`)
   - ✅ 服务生命周期管理
   - ✅ 优雅启动和关闭
   - ✅ 缓存预热
   - ✅ 后台任务管理

## 📊 系统架构

```
AI Server → MQTT Broker → 计费API → MySQL/Redis
    ↓           ↓           ↓          ↓
  用量上报    消息传递      业务处理    数据存储
```

## 🔌 MQTT 消息格式

### 用量上报消息 (Topic: `usage-report`)
```json
{
    "api_key": "abc123",
    "module": "llm" // llm | tts | asr
    "model": "gpt-3.5-turbo", // 模型名称
    "usage": 1500, // 用量
    "metadata": {"user_id": "test-user"}, // 元数据
    "timestamp": 172638868399 // 时间戳
}
```

### Key状态推送 (Topic: `key-status-updates`)

```json
{
    "updates": [
        {
            "key": "abc123",
            "status": "blocked" // ok | blocked,
            "reason": "quota exhausted"
        },
        {
            "key": "def456",
            "status": "blocked" // ok | blocked,
            "reason": "quota expired"
        }
    ],
    "timestamp": 172638868399
}
```

## 🌐 HTTP API 使用示例

### 1. 查询Key状态
```bash
curl -X GET "http://localhost:8080/api/billing/keys/abc123"
```

响应:
```json
{
    "api_key": "abc123",
    "status": "ok",
    "available": true,
    "quotas": [
        {
            "module": "llm",
            "quota": 100000,
            "used": 15000,
            "available": 85000
        }
    ]
}
```

### 2. 更新Key配额
```bash
curl -X POST "http://localhost:8080/api/billing/keys/abc123/quota" \
  -H "Content-Type: application/json" \
  -d '{
    "module": "llm",
    "quota": 200000,
    "description": "增加LLM配额"
  }'
```

### 3. 获取用量历史
```bash
curl -X GET "http://localhost:8080/api/billing/keys/abc123/usage?page=1&size=20&module=llm"
```

### 4. 获取用量统计
```bash
curl -X GET "http://localhost:8080/api/billing/keys/abc123/stats?period=month"
```

## 📋 核心业务流程

### 1. 用量上报流程
1. AI Server 通过MQTT发送用量报告到 `usage-report` 主题
2. 计费服务接收消息并验证格式
3. 查询Key和价格规则
4. 计算费用并记录到用量日志表
5. 更新Key配额使用量
6. 检查是否超额，如果超额则更新Key状态为blocked
7. 通过MQTT推送Key状态更新

### 2. Key状态查询流程
1. 客户端调用HTTP API查询Key状态
2. 先从Redis缓存获取，如果命中直接返回
3. 如果缓存未命中，从数据库查询Key和配额信息
4. 计算可用状态并更新缓存
5. 返回完整的Key状态信息

### 3. 配额管理流程
1. 管理员通过HTTP API更新Key配额
2. 更新数据库中的配额记录
3. 清除相关缓存确保数据一致性
4. 如果Key之前被阻止且现在有足够配额，恢复Key状态

## 🔧 性能优化

1. **Redis缓存**：缓存Key状态、配额使用量、价格规则
2. **数据库索引**：Key字段、时间戳字段建立索引
3. **MQTT QoS=1**：确保消息可靠传递
4. **批量处理**：支持批量用量上报
5. **连接池**：数据库和Redis连接池优化

## 📈 扩展方向

1. **多租户支持**：添加租户隔离
2. **实时监控**：集成Prometheus监控
3. **API限流**：防止恶意请求
4. **数据分析**：使用报表和仪表板
5. **多币种计费**：支持不同币种
6. **预付费模式**：支持预充值消费

---
