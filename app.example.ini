[app]
PageSize  = 20
JwtSecret = CB3F5CFC-7BEE-4847-9756-9F00A5711C53

[server]
Host    = 127.0.0.1
Port    = 9000
RunMode = debug

[database]
Host        = 127.0.0.1
Port        = 3306
User        =
Password    =
Name        =
TablePrefix =

[redis]
Addr     = 127.0.0.1:6379
Password =
DB       = 0
Prefix   =

[mail]
Host     =
Port     =
Email    =
Password =

[oss]
AccessKeyId     =
AccessKeySecret =
EndPoint        =
BucketName      =
BaseUrl         =

[sls]
AccessKeyId     =
AccessKeySecret =
EndPoint        =
ProjectName     =
LogStoreName    =
Source          =

[crypto]
Chacha20Key   =
Chacha20Nonce =
AESKey        =
AESIv         =

[billing]
MQTTBrokerURL = ssl://host:port
MQTTUsername  = username
MQTTPassword  = password
CacheTTL      = 300  # 缓存过期时间（秒）

