package query

import (
	"errors"

	"git.uozi.org/uozi/potato-billing-api/model"
	"github.com/uozi-tech/cosy/logger"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func Init(db *gorm.DB) {
	SetDefault(db)

	// Create initial user
	u := User
	_, err := u.Unscoped().Where(u.ID.Eq(1)).First()

	if errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Info("Creating initial user, email is admin, password is admin")
		pwd, err := bcrypt.GenerateFromPassword([]byte("admin"), bcrypt.DefaultCost)
		if err != nil {
			logger.Fatal(err)
		}

		_, err = u.Unscoped().Where(u.ID.Eq(1)).Attrs(field.Attrs(&model.User{
			// todo: 替换为真实的超管邮箱，可以在配置文件配置，以便后续发送邮件通知
			Email:    "<EMAIL>",
			Password: string(pwd),
			Name:     "admin",
			Status:   model.UserStatusActive,
		})).FirstOrCreate()

		if err != nil {
			logger.Fatal("Fail to create initial user", err)
		}
	}
}
