// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-billing-api/model"
)

func newUpload(db *gorm.DB, opts ...gen.DOOption) upload {
	_upload := upload{}

	_upload.uploadDo.UseDB(db, opts...)
	_upload.uploadDo.UseModel(&model.Upload{})

	tableName := _upload.uploadDo.TableName()
	_upload.ALL = field.NewAsterisk(tableName)
	_upload.ID = field.NewUint64(tableName, "id")
	_upload.CreatedAt = field.NewInt64(tableName, "created_at")
	_upload.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_upload.DeletedAt = field.NewUint(tableName, "deleted_at")
	_upload.UserId = field.NewUint64(tableName, "user_id")
	_upload.MIME = field.NewString(tableName, "mime")
	_upload.Name = field.NewString(tableName, "name")
	_upload.Path = field.NewString(tableName, "path")
	_upload.Thumbnail = field.NewString(tableName, "thumbnail")
	_upload.Size = field.NewInt64(tableName, "size")
	_upload.To = field.NewString(tableName, "to")
	_upload.User = uploadBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Avatar.User", "model.User"),
			},
		},
	}

	_upload.fillFieldMap()

	return _upload
}

type upload struct {
	uploadDo

	ALL       field.Asterisk
	ID        field.Uint64
	CreatedAt field.Int64
	UpdatedAt field.Int64
	DeletedAt field.Uint
	UserId    field.Uint64
	MIME      field.String
	Name      field.String
	Path      field.String
	Thumbnail field.String
	Size      field.Int64
	To        field.String
	User      uploadBelongsToUser

	fieldMap map[string]field.Expr
}

func (u upload) Table(newTableName string) *upload {
	u.uploadDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u upload) As(alias string) *upload {
	u.uploadDo.DO = *(u.uploadDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *upload) updateTableName(table string) *upload {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewUint64(table, "id")
	u.CreatedAt = field.NewInt64(table, "created_at")
	u.UpdatedAt = field.NewInt64(table, "updated_at")
	u.DeletedAt = field.NewUint(table, "deleted_at")
	u.UserId = field.NewUint64(table, "user_id")
	u.MIME = field.NewString(table, "mime")
	u.Name = field.NewString(table, "name")
	u.Path = field.NewString(table, "path")
	u.Thumbnail = field.NewString(table, "thumbnail")
	u.Size = field.NewInt64(table, "size")
	u.To = field.NewString(table, "to")

	u.fillFieldMap()

	return u
}

func (u *upload) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *upload) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 12)
	u.fieldMap["id"] = u.ID
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["deleted_at"] = u.DeletedAt
	u.fieldMap["user_id"] = u.UserId
	u.fieldMap["mime"] = u.MIME
	u.fieldMap["name"] = u.Name
	u.fieldMap["path"] = u.Path
	u.fieldMap["thumbnail"] = u.Thumbnail
	u.fieldMap["size"] = u.Size
	u.fieldMap["to"] = u.To

}

func (u upload) clone(db *gorm.DB) upload {
	u.uploadDo.ReplaceConnPool(db.Statement.ConnPool)
	u.User.db = db.Session(&gorm.Session{Initialized: true})
	u.User.db.Statement.ConnPool = db.Statement.ConnPool
	return u
}

func (u upload) replaceDB(db *gorm.DB) upload {
	u.uploadDo.ReplaceDB(db)
	u.User.db = db.Session(&gorm.Session{})
	return u
}

type uploadBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
}

func (a uploadBelongsToUser) Where(conds ...field.Expr) *uploadBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a uploadBelongsToUser) WithContext(ctx context.Context) *uploadBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a uploadBelongsToUser) Session(session *gorm.Session) *uploadBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a uploadBelongsToUser) Model(m *model.Upload) *uploadBelongsToUserTx {
	return &uploadBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a uploadBelongsToUser) Unscoped() *uploadBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type uploadBelongsToUserTx struct{ tx *gorm.Association }

func (a uploadBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a uploadBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a uploadBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a uploadBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a uploadBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a uploadBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a uploadBelongsToUserTx) Unscoped() *uploadBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type uploadDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (u uploadDo) FirstByID(id uint64) (result *model.Upload, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = u.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (u uploadDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update uploads set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = u.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (u uploadDo) Debug() *uploadDo {
	return u.withDO(u.DO.Debug())
}

func (u uploadDo) WithContext(ctx context.Context) *uploadDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u uploadDo) ReadDB() *uploadDo {
	return u.Clauses(dbresolver.Read)
}

func (u uploadDo) WriteDB() *uploadDo {
	return u.Clauses(dbresolver.Write)
}

func (u uploadDo) Session(config *gorm.Session) *uploadDo {
	return u.withDO(u.DO.Session(config))
}

func (u uploadDo) Clauses(conds ...clause.Expression) *uploadDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u uploadDo) Returning(value interface{}, columns ...string) *uploadDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u uploadDo) Not(conds ...gen.Condition) *uploadDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u uploadDo) Or(conds ...gen.Condition) *uploadDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u uploadDo) Select(conds ...field.Expr) *uploadDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u uploadDo) Where(conds ...gen.Condition) *uploadDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u uploadDo) Order(conds ...field.Expr) *uploadDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u uploadDo) Distinct(cols ...field.Expr) *uploadDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u uploadDo) Omit(cols ...field.Expr) *uploadDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u uploadDo) Join(table schema.Tabler, on ...field.Expr) *uploadDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u uploadDo) LeftJoin(table schema.Tabler, on ...field.Expr) *uploadDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u uploadDo) RightJoin(table schema.Tabler, on ...field.Expr) *uploadDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u uploadDo) Group(cols ...field.Expr) *uploadDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u uploadDo) Having(conds ...gen.Condition) *uploadDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u uploadDo) Limit(limit int) *uploadDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u uploadDo) Offset(offset int) *uploadDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u uploadDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *uploadDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u uploadDo) Unscoped() *uploadDo {
	return u.withDO(u.DO.Unscoped())
}

func (u uploadDo) Create(values ...*model.Upload) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u uploadDo) CreateInBatches(values []*model.Upload, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u uploadDo) Save(values ...*model.Upload) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u uploadDo) First() (*model.Upload, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Upload), nil
	}
}

func (u uploadDo) Take() (*model.Upload, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Upload), nil
	}
}

func (u uploadDo) Last() (*model.Upload, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Upload), nil
	}
}

func (u uploadDo) Find() ([]*model.Upload, error) {
	result, err := u.DO.Find()
	return result.([]*model.Upload), err
}

func (u uploadDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Upload, err error) {
	buf := make([]*model.Upload, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u uploadDo) FindInBatches(result *[]*model.Upload, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u uploadDo) Attrs(attrs ...field.AssignExpr) *uploadDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u uploadDo) Assign(attrs ...field.AssignExpr) *uploadDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u uploadDo) Joins(fields ...field.RelationField) *uploadDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u uploadDo) Preload(fields ...field.RelationField) *uploadDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u uploadDo) FirstOrInit() (*model.Upload, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Upload), nil
	}
}

func (u uploadDo) FirstOrCreate() (*model.Upload, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Upload), nil
	}
}

func (u uploadDo) FindByPage(offset int, limit int) (result []*model.Upload, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u uploadDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u uploadDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u uploadDo) Delete(models ...*model.Upload) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *uploadDo) withDO(do gen.Dao) *uploadDo {
	u.DO = *do.(*gen.DO)
	return u
}
