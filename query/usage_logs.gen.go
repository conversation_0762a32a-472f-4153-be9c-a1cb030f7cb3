// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-billing-api/model"
)

func newUsageLog(db *gorm.DB, opts ...gen.DOOption) usageLog {
	_usageLog := usageLog{}

	_usageLog.usageLogDo.UseDB(db, opts...)
	_usageLog.usageLogDo.UseModel(&model.UsageLog{})

	tableName := _usageLog.usageLogDo.TableName()
	_usageLog.ALL = field.NewAsterisk(tableName)
	_usageLog.ID = field.NewUint64(tableName, "id")
	_usageLog.CreatedAt = field.NewInt64(tableName, "created_at")
	_usageLog.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_usageLog.DeletedAt = field.NewUint(tableName, "deleted_at")
	_usageLog.APIKey = field.NewString(tableName, "api_key")
	_usageLog.Module = field.NewString(tableName, "module")
	_usageLog.ModelName = field.NewString(tableName, "model_name")
	_usageLog.Usage = field.NewInt64(tableName, "usage")
	_usageLog.UnitPrice = field.NewFloat64(tableName, "unit_price")
	_usageLog.Cost = field.NewFloat64(tableName, "cost")
	_usageLog.Currency = field.NewString(tableName, "currency")
	_usageLog.Unit = field.NewString(tableName, "unit")
	_usageLog.PricingRuleID = field.NewUint64(tableName, "pricing_rule_id")
	_usageLog.BillingType = field.NewString(tableName, "billing_type")
	_usageLog.QuotaPackageID = field.NewUint64(tableName, "quota_package_id")
	_usageLog.UserID = field.NewUint64(tableName, "user_id")
	_usageLog.Metadata = field.NewField(tableName, "metadata")

	_usageLog.fillFieldMap()

	return _usageLog
}

type usageLog struct {
	usageLogDo

	ALL            field.Asterisk
	ID             field.Uint64
	CreatedAt      field.Int64
	UpdatedAt      field.Int64
	DeletedAt      field.Uint
	APIKey         field.String
	Module         field.String
	ModelName      field.String
	Usage          field.Int64
	UnitPrice      field.Float64
	Cost           field.Float64
	Currency       field.String
	Unit           field.String
	PricingRuleID  field.Uint64
	BillingType    field.String
	QuotaPackageID field.Uint64
	UserID         field.Uint64
	Metadata       field.Field

	fieldMap map[string]field.Expr
}

func (u usageLog) Table(newTableName string) *usageLog {
	u.usageLogDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u usageLog) As(alias string) *usageLog {
	u.usageLogDo.DO = *(u.usageLogDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *usageLog) updateTableName(table string) *usageLog {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewUint64(table, "id")
	u.CreatedAt = field.NewInt64(table, "created_at")
	u.UpdatedAt = field.NewInt64(table, "updated_at")
	u.DeletedAt = field.NewUint(table, "deleted_at")
	u.APIKey = field.NewString(table, "api_key")
	u.Module = field.NewString(table, "module")
	u.ModelName = field.NewString(table, "model_name")
	u.Usage = field.NewInt64(table, "usage")
	u.UnitPrice = field.NewFloat64(table, "unit_price")
	u.Cost = field.NewFloat64(table, "cost")
	u.Currency = field.NewString(table, "currency")
	u.Unit = field.NewString(table, "unit")
	u.PricingRuleID = field.NewUint64(table, "pricing_rule_id")
	u.BillingType = field.NewString(table, "billing_type")
	u.QuotaPackageID = field.NewUint64(table, "quota_package_id")
	u.UserID = field.NewUint64(table, "user_id")
	u.Metadata = field.NewField(table, "metadata")

	u.fillFieldMap()

	return u
}

func (u *usageLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *usageLog) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 17)
	u.fieldMap["id"] = u.ID
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["deleted_at"] = u.DeletedAt
	u.fieldMap["api_key"] = u.APIKey
	u.fieldMap["module"] = u.Module
	u.fieldMap["model_name"] = u.ModelName
	u.fieldMap["usage"] = u.Usage
	u.fieldMap["unit_price"] = u.UnitPrice
	u.fieldMap["cost"] = u.Cost
	u.fieldMap["currency"] = u.Currency
	u.fieldMap["unit"] = u.Unit
	u.fieldMap["pricing_rule_id"] = u.PricingRuleID
	u.fieldMap["billing_type"] = u.BillingType
	u.fieldMap["quota_package_id"] = u.QuotaPackageID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["metadata"] = u.Metadata
}

func (u usageLog) clone(db *gorm.DB) usageLog {
	u.usageLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u usageLog) replaceDB(db *gorm.DB) usageLog {
	u.usageLogDo.ReplaceDB(db)
	return u
}

type usageLogDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (u usageLogDo) FirstByID(id uint64) (result *model.UsageLog, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = u.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (u usageLogDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update usage_logs set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = u.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (u usageLogDo) Debug() *usageLogDo {
	return u.withDO(u.DO.Debug())
}

func (u usageLogDo) WithContext(ctx context.Context) *usageLogDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u usageLogDo) ReadDB() *usageLogDo {
	return u.Clauses(dbresolver.Read)
}

func (u usageLogDo) WriteDB() *usageLogDo {
	return u.Clauses(dbresolver.Write)
}

func (u usageLogDo) Session(config *gorm.Session) *usageLogDo {
	return u.withDO(u.DO.Session(config))
}

func (u usageLogDo) Clauses(conds ...clause.Expression) *usageLogDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u usageLogDo) Returning(value interface{}, columns ...string) *usageLogDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u usageLogDo) Not(conds ...gen.Condition) *usageLogDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u usageLogDo) Or(conds ...gen.Condition) *usageLogDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u usageLogDo) Select(conds ...field.Expr) *usageLogDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u usageLogDo) Where(conds ...gen.Condition) *usageLogDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u usageLogDo) Order(conds ...field.Expr) *usageLogDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u usageLogDo) Distinct(cols ...field.Expr) *usageLogDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u usageLogDo) Omit(cols ...field.Expr) *usageLogDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u usageLogDo) Join(table schema.Tabler, on ...field.Expr) *usageLogDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u usageLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *usageLogDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u usageLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *usageLogDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u usageLogDo) Group(cols ...field.Expr) *usageLogDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u usageLogDo) Having(conds ...gen.Condition) *usageLogDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u usageLogDo) Limit(limit int) *usageLogDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u usageLogDo) Offset(offset int) *usageLogDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u usageLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *usageLogDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u usageLogDo) Unscoped() *usageLogDo {
	return u.withDO(u.DO.Unscoped())
}

func (u usageLogDo) Create(values ...*model.UsageLog) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u usageLogDo) CreateInBatches(values []*model.UsageLog, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u usageLogDo) Save(values ...*model.UsageLog) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u usageLogDo) First() (*model.UsageLog, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UsageLog), nil
	}
}

func (u usageLogDo) Take() (*model.UsageLog, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UsageLog), nil
	}
}

func (u usageLogDo) Last() (*model.UsageLog, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UsageLog), nil
	}
}

func (u usageLogDo) Find() ([]*model.UsageLog, error) {
	result, err := u.DO.Find()
	return result.([]*model.UsageLog), err
}

func (u usageLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UsageLog, err error) {
	buf := make([]*model.UsageLog, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u usageLogDo) FindInBatches(result *[]*model.UsageLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u usageLogDo) Attrs(attrs ...field.AssignExpr) *usageLogDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u usageLogDo) Assign(attrs ...field.AssignExpr) *usageLogDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u usageLogDo) Joins(fields ...field.RelationField) *usageLogDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u usageLogDo) Preload(fields ...field.RelationField) *usageLogDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u usageLogDo) FirstOrInit() (*model.UsageLog, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UsageLog), nil
	}
}

func (u usageLogDo) FirstOrCreate() (*model.UsageLog, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UsageLog), nil
	}
}

func (u usageLogDo) FindByPage(offset int, limit int) (result []*model.UsageLog, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u usageLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u usageLogDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u usageLogDo) Delete(models ...*model.UsageLog) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *usageLogDo) withDO(do gen.Dao) *usageLogDo {
	u.DO = *do.(*gen.DO)
	return u
}
