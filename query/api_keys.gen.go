// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-billing-api/model"
)

func newApiKey(db *gorm.DB, opts ...gen.DOOption) apiKey {
	_apiKey := apiKey{}

	_apiKey.apiKeyDo.UseDB(db, opts...)
	_apiKey.apiKeyDo.UseModel(&model.ApiKey{})

	tableName := _apiKey.apiKeyDo.TableName()
	_apiKey.ALL = field.NewAsterisk(tableName)
	_apiKey.ID = field.NewUint64(tableName, "id")
	_apiKey.CreatedAt = field.NewInt64(tableName, "created_at")
	_apiKey.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_apiKey.DeletedAt = field.NewUint(tableName, "deleted_at")
	_apiKey.APIKey = field.NewString(tableName, "api_key")
	_apiKey.Name = field.NewString(tableName, "name")
	_apiKey.Module = field.NewString(tableName, "module")
	_apiKey.Status = field.NewString(tableName, "status")
	_apiKey.UserID = field.NewUint64(tableName, "user_id")
	_apiKey.Comment = field.NewString(tableName, "comment")
	_apiKey.User = apiKeyBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Avatar.User", "model.User"),
			},
		},
	}

	_apiKey.fillFieldMap()

	return _apiKey
}

type apiKey struct {
	apiKeyDo

	ALL       field.Asterisk
	ID        field.Uint64
	CreatedAt field.Int64
	UpdatedAt field.Int64
	DeletedAt field.Uint
	APIKey    field.String
	Name      field.String
	Module    field.String
	Status    field.String
	UserID    field.Uint64
	Comment   field.String
	User      apiKeyBelongsToUser

	fieldMap map[string]field.Expr
}

func (a apiKey) Table(newTableName string) *apiKey {
	a.apiKeyDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a apiKey) As(alias string) *apiKey {
	a.apiKeyDo.DO = *(a.apiKeyDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *apiKey) updateTableName(table string) *apiKey {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewUint64(table, "id")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.DeletedAt = field.NewUint(table, "deleted_at")
	a.APIKey = field.NewString(table, "api_key")
	a.Name = field.NewString(table, "name")
	a.Module = field.NewString(table, "module")
	a.Status = field.NewString(table, "status")
	a.UserID = field.NewUint64(table, "user_id")
	a.Comment = field.NewString(table, "comment")

	a.fillFieldMap()

	return a
}

func (a *apiKey) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *apiKey) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 11)
	a.fieldMap["id"] = a.ID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
	a.fieldMap["api_key"] = a.APIKey
	a.fieldMap["name"] = a.Name
	a.fieldMap["module"] = a.Module
	a.fieldMap["status"] = a.Status
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["comment"] = a.Comment

}

func (a apiKey) clone(db *gorm.DB) apiKey {
	a.apiKeyDo.ReplaceConnPool(db.Statement.ConnPool)
	a.User.db = db.Session(&gorm.Session{Initialized: true})
	a.User.db.Statement.ConnPool = db.Statement.ConnPool
	return a
}

func (a apiKey) replaceDB(db *gorm.DB) apiKey {
	a.apiKeyDo.ReplaceDB(db)
	a.User.db = db.Session(&gorm.Session{})
	return a
}

type apiKeyBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
}

func (a apiKeyBelongsToUser) Where(conds ...field.Expr) *apiKeyBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a apiKeyBelongsToUser) WithContext(ctx context.Context) *apiKeyBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a apiKeyBelongsToUser) Session(session *gorm.Session) *apiKeyBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a apiKeyBelongsToUser) Model(m *model.ApiKey) *apiKeyBelongsToUserTx {
	return &apiKeyBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a apiKeyBelongsToUser) Unscoped() *apiKeyBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type apiKeyBelongsToUserTx struct{ tx *gorm.Association }

func (a apiKeyBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a apiKeyBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a apiKeyBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a apiKeyBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a apiKeyBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a apiKeyBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a apiKeyBelongsToUserTx) Unscoped() *apiKeyBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type apiKeyDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (a apiKeyDo) FirstByID(id uint64) (result *model.ApiKey, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = a.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (a apiKeyDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update api_keys set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = a.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (a apiKeyDo) Debug() *apiKeyDo {
	return a.withDO(a.DO.Debug())
}

func (a apiKeyDo) WithContext(ctx context.Context) *apiKeyDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a apiKeyDo) ReadDB() *apiKeyDo {
	return a.Clauses(dbresolver.Read)
}

func (a apiKeyDo) WriteDB() *apiKeyDo {
	return a.Clauses(dbresolver.Write)
}

func (a apiKeyDo) Session(config *gorm.Session) *apiKeyDo {
	return a.withDO(a.DO.Session(config))
}

func (a apiKeyDo) Clauses(conds ...clause.Expression) *apiKeyDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a apiKeyDo) Returning(value interface{}, columns ...string) *apiKeyDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a apiKeyDo) Not(conds ...gen.Condition) *apiKeyDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a apiKeyDo) Or(conds ...gen.Condition) *apiKeyDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a apiKeyDo) Select(conds ...field.Expr) *apiKeyDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a apiKeyDo) Where(conds ...gen.Condition) *apiKeyDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a apiKeyDo) Order(conds ...field.Expr) *apiKeyDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a apiKeyDo) Distinct(cols ...field.Expr) *apiKeyDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a apiKeyDo) Omit(cols ...field.Expr) *apiKeyDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a apiKeyDo) Join(table schema.Tabler, on ...field.Expr) *apiKeyDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a apiKeyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *apiKeyDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a apiKeyDo) RightJoin(table schema.Tabler, on ...field.Expr) *apiKeyDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a apiKeyDo) Group(cols ...field.Expr) *apiKeyDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a apiKeyDo) Having(conds ...gen.Condition) *apiKeyDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a apiKeyDo) Limit(limit int) *apiKeyDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a apiKeyDo) Offset(offset int) *apiKeyDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a apiKeyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *apiKeyDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a apiKeyDo) Unscoped() *apiKeyDo {
	return a.withDO(a.DO.Unscoped())
}

func (a apiKeyDo) Create(values ...*model.ApiKey) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a apiKeyDo) CreateInBatches(values []*model.ApiKey, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a apiKeyDo) Save(values ...*model.ApiKey) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a apiKeyDo) First() (*model.ApiKey, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApiKey), nil
	}
}

func (a apiKeyDo) Take() (*model.ApiKey, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApiKey), nil
	}
}

func (a apiKeyDo) Last() (*model.ApiKey, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApiKey), nil
	}
}

func (a apiKeyDo) Find() ([]*model.ApiKey, error) {
	result, err := a.DO.Find()
	return result.([]*model.ApiKey), err
}

func (a apiKeyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ApiKey, err error) {
	buf := make([]*model.ApiKey, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a apiKeyDo) FindInBatches(result *[]*model.ApiKey, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a apiKeyDo) Attrs(attrs ...field.AssignExpr) *apiKeyDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a apiKeyDo) Assign(attrs ...field.AssignExpr) *apiKeyDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a apiKeyDo) Joins(fields ...field.RelationField) *apiKeyDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a apiKeyDo) Preload(fields ...field.RelationField) *apiKeyDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a apiKeyDo) FirstOrInit() (*model.ApiKey, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApiKey), nil
	}
}

func (a apiKeyDo) FirstOrCreate() (*model.ApiKey, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ApiKey), nil
	}
}

func (a apiKeyDo) FindByPage(offset int, limit int) (result []*model.ApiKey, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a apiKeyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a apiKeyDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a apiKeyDo) Delete(models ...*model.ApiKey) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *apiKeyDo) withDO(do gen.Dao) *apiKeyDo {
	a.DO = *do.(*gen.DO)
	return a
}
