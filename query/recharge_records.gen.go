// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.uozi.org/uozi/potato-billing-api/model"
)

func newRechargeRecord(db *gorm.DB, opts ...gen.DOOption) rechargeRecord {
	_rechargeRecord := rechargeRecord{}

	_rechargeRecord.rechargeRecordDo.UseDB(db, opts...)
	_rechargeRecord.rechargeRecordDo.UseModel(&model.RechargeRecord{})

	tableName := _rechargeRecord.rechargeRecordDo.TableName()
	_rechargeRecord.ALL = field.NewAsterisk(tableName)
	_rechargeRecord.ID = field.NewUint64(tableName, "id")
	_rechargeRecord.CreatedAt = field.NewInt64(tableName, "created_at")
	_rechargeRecord.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_rechargeRecord.DeletedAt = field.NewUint(tableName, "deleted_at")
	_rechargeRecord.UserID = field.NewUint64(tableName, "user_id")
	_rechargeRecord.Amount = field.NewFloat64(tableName, "amount")
	_rechargeRecord.Type = field.NewString(tableName, "type")
	_rechargeRecord.Status = field.NewString(tableName, "status")
	_rechargeRecord.TradeNo = field.NewString(tableName, "trade_no")
	_rechargeRecord.Description = field.NewString(tableName, "description")
	_rechargeRecord.Metadata = field.NewField(tableName, "metadata")
	_rechargeRecord.OperatorID = field.NewUint64(tableName, "operator_id")
	_rechargeRecord.User = rechargeRecordBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "model.User"),
		Avatar: struct {
			field.RelationField
			User struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Avatar", "model.Upload"),
			User: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Avatar.User", "model.User"),
			},
		},
	}

	_rechargeRecord.Operator = rechargeRecordBelongsToOperator{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Operator", "model.User"),
	}

	_rechargeRecord.fillFieldMap()

	return _rechargeRecord
}

type rechargeRecord struct {
	rechargeRecordDo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Int64
	UpdatedAt   field.Int64
	DeletedAt   field.Uint
	UserID      field.Uint64
	Amount      field.Float64
	Type        field.String
	Status      field.String
	TradeNo     field.String
	Description field.String
	Metadata    field.Field
	OperatorID  field.Uint64
	User        rechargeRecordBelongsToUser

	Operator rechargeRecordBelongsToOperator

	fieldMap map[string]field.Expr
}

func (r rechargeRecord) Table(newTableName string) *rechargeRecord {
	r.rechargeRecordDo.UseTable(newTableName)
	return r.updateTableName(newTableName)
}

func (r rechargeRecord) As(alias string) *rechargeRecord {
	r.rechargeRecordDo.DO = *(r.rechargeRecordDo.As(alias).(*gen.DO))
	return r.updateTableName(alias)
}

func (r *rechargeRecord) updateTableName(table string) *rechargeRecord {
	r.ALL = field.NewAsterisk(table)
	r.ID = field.NewUint64(table, "id")
	r.CreatedAt = field.NewInt64(table, "created_at")
	r.UpdatedAt = field.NewInt64(table, "updated_at")
	r.DeletedAt = field.NewUint(table, "deleted_at")
	r.UserID = field.NewUint64(table, "user_id")
	r.Amount = field.NewFloat64(table, "amount")
	r.Type = field.NewString(table, "type")
	r.Status = field.NewString(table, "status")
	r.TradeNo = field.NewString(table, "trade_no")
	r.Description = field.NewString(table, "description")
	r.Metadata = field.NewField(table, "metadata")
	r.OperatorID = field.NewUint64(table, "operator_id")

	r.fillFieldMap()

	return r
}

func (r *rechargeRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := r.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (r *rechargeRecord) fillFieldMap() {
	r.fieldMap = make(map[string]field.Expr, 14)
	r.fieldMap["id"] = r.ID
	r.fieldMap["created_at"] = r.CreatedAt
	r.fieldMap["updated_at"] = r.UpdatedAt
	r.fieldMap["deleted_at"] = r.DeletedAt
	r.fieldMap["user_id"] = r.UserID
	r.fieldMap["amount"] = r.Amount
	r.fieldMap["type"] = r.Type
	r.fieldMap["status"] = r.Status
	r.fieldMap["trade_no"] = r.TradeNo
	r.fieldMap["description"] = r.Description
	r.fieldMap["metadata"] = r.Metadata
	r.fieldMap["operator_id"] = r.OperatorID

}

func (r rechargeRecord) clone(db *gorm.DB) rechargeRecord {
	r.rechargeRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	r.User.db = db.Session(&gorm.Session{Initialized: true})
	r.User.db.Statement.ConnPool = db.Statement.ConnPool
	r.Operator.db = db.Session(&gorm.Session{Initialized: true})
	r.Operator.db.Statement.ConnPool = db.Statement.ConnPool
	return r
}

func (r rechargeRecord) replaceDB(db *gorm.DB) rechargeRecord {
	r.rechargeRecordDo.ReplaceDB(db)
	r.User.db = db.Session(&gorm.Session{})
	r.Operator.db = db.Session(&gorm.Session{})
	return r
}

type rechargeRecordBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Avatar struct {
		field.RelationField
		User struct {
			field.RelationField
		}
	}
}

func (a rechargeRecordBelongsToUser) Where(conds ...field.Expr) *rechargeRecordBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a rechargeRecordBelongsToUser) WithContext(ctx context.Context) *rechargeRecordBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a rechargeRecordBelongsToUser) Session(session *gorm.Session) *rechargeRecordBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a rechargeRecordBelongsToUser) Model(m *model.RechargeRecord) *rechargeRecordBelongsToUserTx {
	return &rechargeRecordBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

func (a rechargeRecordBelongsToUser) Unscoped() *rechargeRecordBelongsToUser {
	a.db = a.db.Unscoped()
	return &a
}

type rechargeRecordBelongsToUserTx struct{ tx *gorm.Association }

func (a rechargeRecordBelongsToUserTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a rechargeRecordBelongsToUserTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a rechargeRecordBelongsToUserTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a rechargeRecordBelongsToUserTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a rechargeRecordBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a rechargeRecordBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

func (a rechargeRecordBelongsToUserTx) Unscoped() *rechargeRecordBelongsToUserTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type rechargeRecordBelongsToOperator struct {
	db *gorm.DB

	field.RelationField
}

func (a rechargeRecordBelongsToOperator) Where(conds ...field.Expr) *rechargeRecordBelongsToOperator {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a rechargeRecordBelongsToOperator) WithContext(ctx context.Context) *rechargeRecordBelongsToOperator {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a rechargeRecordBelongsToOperator) Session(session *gorm.Session) *rechargeRecordBelongsToOperator {
	a.db = a.db.Session(session)
	return &a
}

func (a rechargeRecordBelongsToOperator) Model(m *model.RechargeRecord) *rechargeRecordBelongsToOperatorTx {
	return &rechargeRecordBelongsToOperatorTx{a.db.Model(m).Association(a.Name())}
}

func (a rechargeRecordBelongsToOperator) Unscoped() *rechargeRecordBelongsToOperator {
	a.db = a.db.Unscoped()
	return &a
}

type rechargeRecordBelongsToOperatorTx struct{ tx *gorm.Association }

func (a rechargeRecordBelongsToOperatorTx) Find() (result *model.User, err error) {
	return result, a.tx.Find(&result)
}

func (a rechargeRecordBelongsToOperatorTx) Append(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a rechargeRecordBelongsToOperatorTx) Replace(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a rechargeRecordBelongsToOperatorTx) Delete(values ...*model.User) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a rechargeRecordBelongsToOperatorTx) Clear() error {
	return a.tx.Clear()
}

func (a rechargeRecordBelongsToOperatorTx) Count() int64 {
	return a.tx.Count()
}

func (a rechargeRecordBelongsToOperatorTx) Unscoped() *rechargeRecordBelongsToOperatorTx {
	a.tx = a.tx.Unscoped()
	return &a
}

type rechargeRecordDo struct{ gen.DO }

// FirstByID Where("id=@id")
func (r rechargeRecordDo) FirstByID(id uint64) (result *model.RechargeRecord, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("id=? ")

	var executeSQL *gorm.DB
	executeSQL = r.UnderlyingDB().Where(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// DeleteByID update @@table set deleted_at=NOW() where id=@id
func (r rechargeRecordDo) DeleteByID(id uint64) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("update recharge_records set deleted_at=NOW() where id=? ")

	var executeSQL *gorm.DB
	executeSQL = r.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (r rechargeRecordDo) Debug() *rechargeRecordDo {
	return r.withDO(r.DO.Debug())
}

func (r rechargeRecordDo) WithContext(ctx context.Context) *rechargeRecordDo {
	return r.withDO(r.DO.WithContext(ctx))
}

func (r rechargeRecordDo) ReadDB() *rechargeRecordDo {
	return r.Clauses(dbresolver.Read)
}

func (r rechargeRecordDo) WriteDB() *rechargeRecordDo {
	return r.Clauses(dbresolver.Write)
}

func (r rechargeRecordDo) Session(config *gorm.Session) *rechargeRecordDo {
	return r.withDO(r.DO.Session(config))
}

func (r rechargeRecordDo) Clauses(conds ...clause.Expression) *rechargeRecordDo {
	return r.withDO(r.DO.Clauses(conds...))
}

func (r rechargeRecordDo) Returning(value interface{}, columns ...string) *rechargeRecordDo {
	return r.withDO(r.DO.Returning(value, columns...))
}

func (r rechargeRecordDo) Not(conds ...gen.Condition) *rechargeRecordDo {
	return r.withDO(r.DO.Not(conds...))
}

func (r rechargeRecordDo) Or(conds ...gen.Condition) *rechargeRecordDo {
	return r.withDO(r.DO.Or(conds...))
}

func (r rechargeRecordDo) Select(conds ...field.Expr) *rechargeRecordDo {
	return r.withDO(r.DO.Select(conds...))
}

func (r rechargeRecordDo) Where(conds ...gen.Condition) *rechargeRecordDo {
	return r.withDO(r.DO.Where(conds...))
}

func (r rechargeRecordDo) Order(conds ...field.Expr) *rechargeRecordDo {
	return r.withDO(r.DO.Order(conds...))
}

func (r rechargeRecordDo) Distinct(cols ...field.Expr) *rechargeRecordDo {
	return r.withDO(r.DO.Distinct(cols...))
}

func (r rechargeRecordDo) Omit(cols ...field.Expr) *rechargeRecordDo {
	return r.withDO(r.DO.Omit(cols...))
}

func (r rechargeRecordDo) Join(table schema.Tabler, on ...field.Expr) *rechargeRecordDo {
	return r.withDO(r.DO.Join(table, on...))
}

func (r rechargeRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *rechargeRecordDo {
	return r.withDO(r.DO.LeftJoin(table, on...))
}

func (r rechargeRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *rechargeRecordDo {
	return r.withDO(r.DO.RightJoin(table, on...))
}

func (r rechargeRecordDo) Group(cols ...field.Expr) *rechargeRecordDo {
	return r.withDO(r.DO.Group(cols...))
}

func (r rechargeRecordDo) Having(conds ...gen.Condition) *rechargeRecordDo {
	return r.withDO(r.DO.Having(conds...))
}

func (r rechargeRecordDo) Limit(limit int) *rechargeRecordDo {
	return r.withDO(r.DO.Limit(limit))
}

func (r rechargeRecordDo) Offset(offset int) *rechargeRecordDo {
	return r.withDO(r.DO.Offset(offset))
}

func (r rechargeRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *rechargeRecordDo {
	return r.withDO(r.DO.Scopes(funcs...))
}

func (r rechargeRecordDo) Unscoped() *rechargeRecordDo {
	return r.withDO(r.DO.Unscoped())
}

func (r rechargeRecordDo) Create(values ...*model.RechargeRecord) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Create(values)
}

func (r rechargeRecordDo) CreateInBatches(values []*model.RechargeRecord, batchSize int) error {
	return r.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (r rechargeRecordDo) Save(values ...*model.RechargeRecord) error {
	if len(values) == 0 {
		return nil
	}
	return r.DO.Save(values)
}

func (r rechargeRecordDo) First() (*model.RechargeRecord, error) {
	if result, err := r.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.RechargeRecord), nil
	}
}

func (r rechargeRecordDo) Take() (*model.RechargeRecord, error) {
	if result, err := r.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.RechargeRecord), nil
	}
}

func (r rechargeRecordDo) Last() (*model.RechargeRecord, error) {
	if result, err := r.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.RechargeRecord), nil
	}
}

func (r rechargeRecordDo) Find() ([]*model.RechargeRecord, error) {
	result, err := r.DO.Find()
	return result.([]*model.RechargeRecord), err
}

func (r rechargeRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.RechargeRecord, err error) {
	buf := make([]*model.RechargeRecord, 0, batchSize)
	err = r.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (r rechargeRecordDo) FindInBatches(result *[]*model.RechargeRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return r.DO.FindInBatches(result, batchSize, fc)
}

func (r rechargeRecordDo) Attrs(attrs ...field.AssignExpr) *rechargeRecordDo {
	return r.withDO(r.DO.Attrs(attrs...))
}

func (r rechargeRecordDo) Assign(attrs ...field.AssignExpr) *rechargeRecordDo {
	return r.withDO(r.DO.Assign(attrs...))
}

func (r rechargeRecordDo) Joins(fields ...field.RelationField) *rechargeRecordDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Joins(_f))
	}
	return &r
}

func (r rechargeRecordDo) Preload(fields ...field.RelationField) *rechargeRecordDo {
	for _, _f := range fields {
		r = *r.withDO(r.DO.Preload(_f))
	}
	return &r
}

func (r rechargeRecordDo) FirstOrInit() (*model.RechargeRecord, error) {
	if result, err := r.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.RechargeRecord), nil
	}
}

func (r rechargeRecordDo) FirstOrCreate() (*model.RechargeRecord, error) {
	if result, err := r.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.RechargeRecord), nil
	}
}

func (r rechargeRecordDo) FindByPage(offset int, limit int) (result []*model.RechargeRecord, count int64, err error) {
	result, err = r.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = r.Offset(-1).Limit(-1).Count()
	return
}

func (r rechargeRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = r.Count()
	if err != nil {
		return
	}

	err = r.Offset(offset).Limit(limit).Scan(result)
	return
}

func (r rechargeRecordDo) Scan(result interface{}) (err error) {
	return r.DO.Scan(result)
}

func (r rechargeRecordDo) Delete(models ...*model.RechargeRecord) (result gen.ResultInfo, err error) {
	return r.DO.Delete(models)
}

func (r *rechargeRecordDo) withDO(do gen.Dao) *rechargeRecordDo {
	r.DO = *do.(*gen.DO)
	return r
}
