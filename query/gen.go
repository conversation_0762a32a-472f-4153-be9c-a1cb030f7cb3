// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                  = new(Query)
	ApiKey             *apiKey
	PricingRule        *pricingRule
	QuotaPackageRecord *quotaPackageRecord
	RechargeRecord     *rechargeRecord
	Setting            *setting
	Upload             *upload
	UsageLog           *usageLog
	User               *user
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	ApiKey = &Q.ApiKey
	PricingRule = &Q.PricingRule
	QuotaPackageRecord = &Q.QuotaPackageRecord
	RechargeRecord = &Q.RechargeRecord
	Setting = &Q.Setting
	Upload = &Q.Upload
	UsageLog = &Q.UsageLog
	User = &Q.User
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                 db,
		ApiKey:             newApiKey(db, opts...),
		PricingRule:        newPricingRule(db, opts...),
		QuotaPackageRecord: newQuotaPackageRecord(db, opts...),
		RechargeRecord:     newRechargeRecord(db, opts...),
		Setting:            newSetting(db, opts...),
		Upload:             newUpload(db, opts...),
		UsageLog:           newUsageLog(db, opts...),
		User:               newUser(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	ApiKey             apiKey
	PricingRule        pricingRule
	QuotaPackageRecord quotaPackageRecord
	RechargeRecord     rechargeRecord
	Setting            setting
	Upload             upload
	UsageLog           usageLog
	User               user
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                 db,
		ApiKey:             q.ApiKey.clone(db),
		PricingRule:        q.PricingRule.clone(db),
		QuotaPackageRecord: q.QuotaPackageRecord.clone(db),
		RechargeRecord:     q.RechargeRecord.clone(db),
		Setting:            q.Setting.clone(db),
		Upload:             q.Upload.clone(db),
		UsageLog:           q.UsageLog.clone(db),
		User:               q.User.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                 db,
		ApiKey:             q.ApiKey.replaceDB(db),
		PricingRule:        q.PricingRule.replaceDB(db),
		QuotaPackageRecord: q.QuotaPackageRecord.replaceDB(db),
		RechargeRecord:     q.RechargeRecord.replaceDB(db),
		Setting:            q.Setting.replaceDB(db),
		Upload:             q.Upload.replaceDB(db),
		UsageLog:           q.UsageLog.replaceDB(db),
		User:               q.User.replaceDB(db),
	}
}

type queryCtx struct {
	ApiKey             *apiKeyDo
	PricingRule        *pricingRuleDo
	QuotaPackageRecord *quotaPackageRecordDo
	RechargeRecord     *rechargeRecordDo
	Setting            *settingDo
	Upload             *uploadDo
	UsageLog           *usageLogDo
	User               *userDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		ApiKey:             q.ApiKey.WithContext(ctx),
		PricingRule:        q.PricingRule.WithContext(ctx),
		QuotaPackageRecord: q.QuotaPackageRecord.WithContext(ctx),
		RechargeRecord:     q.RechargeRecord.WithContext(ctx),
		Setting:            q.Setting.WithContext(ctx),
		Upload:             q.Upload.WithContext(ctx),
		UsageLog:           q.UsageLog.WithContext(ctx),
		User:               q.User.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
